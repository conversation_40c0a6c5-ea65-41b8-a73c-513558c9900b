import { combineReducers, configureStore } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
// import sessionStorage from "redux-persist/es/storage/session";
import setManageAccountDataReducer from "./ManageAccount/ManageAccountSlice";
import getAllLanguage from "./ManageAccount/GetAllLanguages";
import ChangePasswordSlice from "./ManageAccount/ChangePasswordSlice";
import { toastReducer } from "../../utils/toastSlice";
import getAllCountriesSlice from "./ManageAccount/GetAllCountryDetails";
import getAllPaymentsCardsSlice from "./ManageAccount/GetPaymentsCards";
import getAllCountryCodesSlice from "./ManageAccount/GetAllCountryCode";
import getAllMangeCompanySlice from "./ManageCompany/GetAllMangeCompanySlice";
import adminLoginSlice from "./AdminLoginSlice";
import getAllTeamMembersSlice from "./ManageCompany/GetAllTeamMembers";
import getAllRolesSlice from "./ManageCompany/DisplayAllRoles";
import AccountDetailsSlice from "./ManageAccount/AccountDetailsSlice";
import editedCompanyDetailsSlice from "./ManageCompany/UpdateCompanyDetails";
import CompanyDetailsSlice from "./ManageCompany/CompanyDetailsSlice";
import allContactsSlice from "./Contacts/AllContactsSlice";
import allDeletedContactsSlice from "./Contacts/GetDeletedContacts";
import addContactSlice from "./Contacts/CreateContactSlice";
import addContactTagsSlice from "./Contacts/CreateContactTags";
import ManageNotificationsSlice from "./ManageNotifications/ManageNotificationsSlice";
import HelpCenterSlice from "./HelpCenter/HelpCenterSlice";
import InboxContactsSlice from "./Inbox/InboxContactsSlice";
import getContactTagsSlice from "./Contacts/getContactTags";
import InboxContactConversationsSlice from "./Inbox/InboxContactConversations";
import ManagePermissionsSlice from "./ManagePermissions/ManagePermissionsSlice";
import getAllTeamMembersByCompanyIdSlice from "./ManageCompany/GetAllTeamMembersByCompanyId";
import InboxContactAssignmentSlice from "./Inbox/InboxContactAssignment";
import InboxContactDetailsSlice from "./Inbox/InboxContactDetails";
import getAllCientSlice from "./ManageClients/getAllClientSlice";
import editContactTagsSlice from "./Contacts/UpdateContactTags";
import SubscriptionSlice from "./Subscription/SubscriptionSlice";
import allTemplatesByCompanyIdSlice from "./Templates/AllTemplatesSlice";
import createCampaignSlice from "./Campaign/CreateCampaignSlice";
import GetCampaignSlice from "./Campaign/GetCampaignSlice";
import createTemplateSlice from "./Templates/CreateTemplateSlice";
import updateTemplateSlice from "./Templates/UpdateTemplateSlice";
import removeTemplateSlice from "./Templates/DeleteTemplateSlice";
import restoreTemplateSlice from "./Templates/RestoreTemplateSlice";
import getUserPermissionSlice from "./ManagePermissions/GetUserPermissionSlice";
import templateByIdSlice from "./Templates/TemplateById";
import WalletSlice from "./Wallet/WalletSlice";
import uploadFileSlice from "./Templates/uploadFileSlice";
import updateCampaignSlice from "./Campaign/EditCampaignSlice";
import GetScheduledCampaignSlice from "./Campaign/GetScheduledCampaignSlice";
import updateInboxSettingsSlice from "./Automation/UpdateInboxSettingsSlice";
import getInboxSettingsSlice from "./Automation/GetInboxSettingsSlice";
import getContactColumnsSlice from "./Automation/GetContactColumnNames";
import getWorkingHoursSlice from "./Automation/GetWorkingHoursSlice";
import createWorkingHoursSlice from "./Automation/createWorkingHoursSlice";
import deleteContactTagsSlice from "./Contacts/DeleteContactTags";
import addContactNotesSlice from "./Contacts/CreateContactNotes";
import deleteContactNotesSlice from "./Contacts/DeleteContactNotes";
import markedAsSpamSlice from "./Inbox/markContactAsSpam";
import deleteContactSlice from "./Contacts/DeleteContactSlice";
import updateContactSlice from "./Contacts/UpdateContactSlice";
import autoReplySlice from "./Automation/autoReplySlice";
import getAutoReplySlice from "./Automation/getAutoReplySlice";
import updateAutoReplySlice from "./Automation/updateAutoReplySlice";
import getCampaignByIdSlice from "./Campaign/GetCampaignByIdSlice";
import deleteAutoReplySlice from "./Automation/deleteAutoReplySlice";
import getWorkflowListSlice from "./Workflows/getWorkflowListSlice";
import getCampaignTitleSlice from "./Campaign/GetCampaignTitleSlice";
import templateNameSlice from "./Templates/GetTemplateNameSlice";
import getVariableNamesSlice from "./Workflows/getVariableNamesSlice";
import createWorkflowCustomMessageSlice from "./Workflows/createWorkflowCustomMessageSlice";
import updateWorkflowListSlice from "./Workflows/updateWorkflowListSlice";
import createWorkflowSlice from "./Workflows/createWorkflowSlice";
import getWorkflowNamesSlice from "./Workflows/getWorkflowNamesSlice";
import getWorkflowSlice from "./Workflows/getWorkflowSlice";
import addVariableNameSlice from "./Workflows/addVariableNamesSlice";
import optedManagementDataSlice from "./Automation/OptedKeywordsSlice";
import optoutKeywordsSlice from "./Automation/GetOptoutKeywords";
import deleteOptedKeywordsSlice from "./Automation/deleteOptedKeywords";
import optinKeywordsSlice from "./Automation/GetOptinKeywords";
import getSelectResponseSlice from "./Workflows/getSelectResponse";
import updateWorkflow from "./Workflows/updateWorkflowSlice";
import agentPerformanceSlice from "./Analytics/AgentAnalyticsSlice";
import agentOverviewSlice from "./Analytics/OverviewAnalyticsSlice";
import inboxAnalysysSlice from "./Analytics/InboxAnalyticsSlice";
import downloadWorkflowByNameSlice from "./Workflows/downloadWorkflowSlice";
import createSelectResponseSlice from "./Workflows/createSelectResponseSlice";
import deleteSelectResponseSlice from "./Workflows/deleteSelectResponseSlice";
import AccountMetaStatusSlice from "./ManageAccount/AccountMetaStatusSlice";
import editTagSlice from "./Contacts/editTagSlice";
import widgetDetailsSlice from "./Integrations/WhatsappWidgetsSlice";
import createAuthTemplateSlice from "./Templates/CreateAuthTemplateSlice";
import previewAuthTemplateSlice from "./Templates/PreviewAuthTemplateSlice";



import CreateBlobIdSlice from "./Campaign/CreateBlobId";
import createAutoReplyMessageSlice from "./Campaign/CreateAutoReplyMessageSlice";
import GetCampaignCountSlice from "./Campaign/GetCampaignCountSlice";
import NoAccessPageSlice from "./Utility/NoAccessDesignPage";
import allContactImportTrackerSlice from "./Contacts/GetAllContactsImportTracker";
import LatestInboxContactsSlice from "./Inbox/LatestInboxContactsSlice";
import createWorkflowReactflowSlice from "./Workflows/createWorkflowReactflowSlice";
import updateWorkflowReactflowSlice from "./Workflows/updateWorkflowReactflowSlice";
import getAllWorkflowsReactflowSlice from "./Workflows/getAllWorkflowsReactflowSlice";
import getWorkflowReactflowByIdSlice from "./Workflows/getWorkflowReactflowByIdSlice";
import createKeywordReactflowSlice from "./Workflows/createKeywordsReactflowSlice";
import deleteKeywordReactflowSlice from "./Workflows/deleteKeywordsReactflowSlice";
import getWorkflowAllKeywordsSlice from "./Workflows/getWorkflowAllKeywordsSlice";
import AddSaveResponseAttributeSlice from "./Workflows/createSaveUserResponseAttribute";
import getSaveResponseAttributeSlice from "./Workflows/getSaveUserResponseAttribute";
import getKeywordsSlice from "./Workflows/getKeywordsSlice";
import toggleWorkflowActiveSlice from "./Workflows/toggleWorkflowActiveSlice";
import createCarouselTemplateSlice from "./Templates/CreateCarouselTemplate";
import getAllWorkflowStatusByTenantIdSlice from "./Utility/GetAllStatusByTenantId";
import GetAllProjectsByTenantIdSlice from "./Utility/GetAllProjectsByTenantId";
import rerunCampaignSlice from "./Campaign/RerunCampaignSlice";
import getFlowstartNodesSlice from "./Workflows/getFlowstartNodesSlice";
import deleteAttributeReactflowSlice from "./Workflows/deleteAttributeReactflowSlice";
import blockContactSlice from "./Inbox/BlockContactSlice";
import unblockContactSlice from "./Inbox/UnblockContactSlice";
import GetAllBlockedContactsSlice from "./Inbox/GetAllBlockedContactsSlice";
import getCampaignAnalyticsDetailsSlice from "./Campaign/GetCampaignAnalyticsDetailsSlice";
import campaignContactsSlice from "./Campaign/CampaignContactsSlice";
import campaignAllFiltersSlice from "./Campaign/CampaignAllFiltersSlice";
const persistConfig = {
  key: "root",
  // storage: sessionStorage,
  storage,
};

const rootReducer = combineReducers({
  adminLogin: adminLoginSlice,
  toastState: toastReducer,

  // Utility

  noAccessPageData: NoAccessPageSlice,

  /*profile/ManageAccount*/
  accountData: AccountDetailsSlice,
  manageAccountData: setManageAccountDataReducer,
  getAllLanguages: getAllLanguage,
  changePassword: ChangePasswordSlice,
  getAllCountries: getAllCountriesSlice,
  getAllPaymentsCard: getAllPaymentsCardsSlice,
  getAllCoutryCodes: getAllCountryCodesSlice,
  accountMetaStatus: AccountMetaStatusSlice,

  /*profile/ManageCompany*/
  getManageCompanies: getAllMangeCompanySlice,
  getAllTeamMembers: getAllTeamMembersSlice,
  getAllTeamMembersByCompanyId: getAllTeamMembersByCompanyIdSlice,
  getAllRoles: getAllRolesSlice,
  updatedCompanyData: editedCompanyDetailsSlice,
  companyData: CompanyDetailsSlice,

  /*contacts*/
  contactsData: allContactsSlice,
  createContactData: addContactSlice,
  updateContactData: updateContactSlice,
  removeContactData: deleteContactSlice,
  createContactTagsData: addContactTagsSlice,
  updateContactTagsData: editContactTagsSlice,
  getContactTagsData: getContactTagsSlice,
  removeContactTagsFromContactData: deleteContactTagsSlice,
  createContactNotesData: addContactNotesSlice,
  removeContactNotesData: deleteContactNotesSlice,
  editTagData: editTagSlice,
  deletedContactsData: allDeletedContactsSlice,

  contactsImportTrackerData: allContactImportTrackerSlice,

  /*profile/ManageNotifications*/
  manageNotifications: ManageNotificationsSlice,

  /*profile/ManagePermissions*/
  managePermissions: ManagePermissionsSlice,
  getUserPermissions: getUserPermissionSlice,
  getAllClients: getAllCientSlice,

  /*profile/HelpCenter*/
  helpCenter: HelpCenterSlice,

  /*Inbox*/
  latestInboxContacts: LatestInboxContactsSlice,
  inboxContactsData: InboxContactsSlice,
  inboxContactConversationsData: InboxContactConversationsSlice,
  inboxContactAssignment: InboxContactAssignmentSlice,
  inboxContactDetails: InboxContactDetailsSlice,
  markedAsSpamData: markedAsSpamSlice,
  blockContactData: blockContactSlice,
  unblockContactData: unblockContactSlice,
  getBlockedContactsData: GetAllBlockedContactsSlice,
  /*Subscription*/
  Subscription: SubscriptionSlice,

  /*Analytics*/
  agentPerformance: agentPerformanceSlice,
  overViewAnalysis: agentOverviewSlice,
  inboxAnalysis: inboxAnalysysSlice,

  /*Templates*/
  allTemplatesData: allTemplatesByCompanyIdSlice,
  previewAuthTemplateData: previewAuthTemplateSlice,
  createAuthTemplateData: createAuthTemplateSlice,
  createTemplateData: createTemplateSlice,
  updateTemplateData: updateTemplateSlice,
  removeTemplateData: removeTemplateSlice,
  restoreTemplateData: restoreTemplateSlice,
  templateByIdData: templateByIdSlice,
  uploadFileData: uploadFileSlice,
  templateNameData: templateNameSlice,
  createCarouselTemplate: createCarouselTemplateSlice,

  /*Campaigns*/
  addCampaign: createCampaignSlice,
  rerunCampaign: rerunCampaignSlice,
  updateCampaign: updateCampaignSlice,
  getCampaign: GetCampaignSlice,
  getScheduledCampaign: GetScheduledCampaignSlice,
  getCampaignById: getCampaignByIdSlice,
  getCampaignTitle: getCampaignTitleSlice,
  addAutoReplyMessage: createAutoReplyMessageSlice,
  getCampaignAnalyticsDetailsData: getCampaignAnalyticsDetailsSlice,
  getCampaignCount: GetCampaignCountSlice,
  createBlobId: CreateBlobIdSlice,
  campaignContactsData: campaignContactsSlice,
  campaignAllFiltersData: campaignAllFiltersSlice,

  /*Wallet*/
  wallet: WalletSlice,

  /*Automation*/
  getWorkingHours: getWorkingHoursSlice,
  getContactColumnsData: getContactColumnsSlice,
  createWorkingHours: createWorkingHoursSlice,
  updateInboxSettings: updateInboxSettingsSlice,
  getInboxSettings: getInboxSettingsSlice,
  optedManagement: optedManagementDataSlice,
  optoutKeywords: optoutKeywordsSlice,
  optinKeywords: optinKeywordsSlice,
  deleteOptedKeywords: deleteOptedKeywordsSlice,
  autoReply: autoReplySlice,
  getAutoReply: getAutoReplySlice,
  updateAutoReply: updateAutoReplySlice,
  deleteAutoReply: deleteAutoReplySlice,
  getWorkflowList: getWorkflowListSlice,
  getVariableNames: getVariableNamesSlice,
  createWorkflowCustomMessage: createWorkflowCustomMessageSlice,
  updateWorkflowList: updateWorkflowListSlice,
  createWorkflow: createWorkflowSlice,
  getWorkflowNames: getWorkflowNamesSlice,
  addVaribleName: addVariableNameSlice,
  getSelectResponseNames: getSelectResponseSlice,
  updateWorkflow: updateWorkflow,
  downloadWorkflowByName: downloadWorkflowByNameSlice,
  createSelectResponse: createSelectResponseSlice,
  deleteSelectResponse: deleteSelectResponseSlice,
  getWorkflow: getWorkflowSlice,

  createWorkflowReactFlow: createWorkflowReactflowSlice,
  updateWorkflowReactFlow: updateWorkflowReactflowSlice,
  getAllWorkflowsReactflow: getAllWorkflowsReactflowSlice,
  getWorkflowReactflowById: getWorkflowReactflowByIdSlice,
  createKeywordReactflow: createKeywordReactflowSlice,
  deleteKeywordReactflow: deleteKeywordReactflowSlice,
  getWorkflowAllKeywords: getWorkflowAllKeywordsSlice,
  AddSaveResponseAttribute: AddSaveResponseAttributeSlice,
  getSaveResponseAttribute: getSaveResponseAttributeSlice,
  getKeywords: getKeywordsSlice,
  toggleWorkflowActive: toggleWorkflowActiveSlice,
  getAllWorkflowStatusByTenantId: getAllWorkflowStatusByTenantIdSlice,
  getAllProjectsByTenantId: GetAllProjectsByTenantIdSlice,
  getFlowstartNodes: getFlowstartNodesSlice,
  deleteAttributeReactflow: deleteAttributeReactflowSlice,


  /*Integartions*/
  widgetData: widgetDetailsSlice,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);
export const store = configureStore({
  reducer: persistedReducer,
});

export const persistor = persistStore(store);
// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;
