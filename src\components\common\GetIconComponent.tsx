import {
  ChecklistOutlined as ChecklistIcon,
  CategoryOutlined as CategoryIcon,
  HelpOutlineOutlined as HelpIcon,
  CampaignOutlined as CampaignIcon,
  VerifiedUserOutlined as VerifiedUserIcon,
  DataUsageOutlined as DataUsageIcon,
  HourglassTop as HourglassIcon,
  TaskOutlined as <PERSON>Icon,
  CancelOutlined as CancelIcon,
  DraftsOutlined as DraftsIcon,
  ArchiveOutlined as ArchiveIcon,
  EventOutlined as EventIcon,
  ChatBubbleOutline as ChatBubbleIcon,
  DoneAll as DoneAllIcon,
  ErrorOutline as ErrorIcon,
  FiberNew as FiberNewIcon,
  RemoveCircleOutline as RemoveCircleIcon,
  PersonOutline as PersonIcon,
  WhatsApp as WhatsAppIcon,
  CloudUploadOutlined as CloudUploadIcon,
  InsertDriveFileOutlined as FileIcon,
} from "@mui/icons-material";

export interface GetIconProps {
  column: string;
  option: string;
  customIcons?: Record<string, { icon: React.ReactNode; label: string }>;
}

const DEFAULT_ICONS: Record<string, { icon: React.ReactNode; label: string }> =
  {
    // Main popover icons
    "1": {
      icon: <ChecklistIcon style={{ fontSize: 20 }} />,
      label: "View All",
    },
    "2": { icon: <CategoryIcon style={{ fontSize: 20 }} />, label: "Category" },
    "3": { icon: <HelpIcon style={{ fontSize: 20 }} />, label: "Status" },
    "4": { icon: <EventIcon style={{ fontSize: 20 }} />, label: "Date" },
    "5": {
      icon: <ChatBubbleIcon style={{ fontSize: 20 }} />,
      label: "Chat Status",
    },
    "6": { icon: <PersonIcon style={{ fontSize: 20 }} />, label: "Source" },

    // Category options icons
    "Category-1": { icon: <CampaignIcon />, label: "Marketing" },
    "Category-2": { icon: <VerifiedUserIcon />, label: "Utility" },
    "Category-3": { icon: <DataUsageIcon />, label: "Authentication" },

    // Status options icons
    "Status-1": {
      icon: <HourglassIcon style={{ fontSize: 20 }} />,
      label: "Pending",
    },
    "Status-2": {
      icon: <TaskIcon style={{ fontSize: 20 }} />,
      label: "Approved",
    },
    "Status-3": {
      icon: <CancelIcon style={{ fontSize: 20 }} />,
      label: "Rejected",
    },
    "Status-4": {
      icon: <DraftsIcon style={{ fontSize: 20 }} />,
      label: "Draft",
    },
    "Status-5": {
      icon: <ArchiveIcon style={{ fontSize: 20 }} />,
      label: "Archived",
    },

    // Chat Status options icons
    "ChatStatus-0": {
      icon: <ChatBubbleIcon style={{ fontSize: 20 }} />,
      label: "Open",
    },
    "ChatStatus-1": {
      icon: <DoneAllIcon style={{ fontSize: 20 }} />,
      label: "Resolved",
    },
    "ChatStatus-2": {
      icon: <ErrorIcon style={{ fontSize: 20, color: "#ef4444" }} />,
      label: "Expired",
    },
    "ChatStatus-3": {
      icon: <FiberNewIcon style={{ fontSize: 20, color: "#3b82f6" }} />,
      label: "New",
    },

    // Source options icons
    "Source-0": {
      icon: <RemoveCircleIcon style={{ fontSize: 20 }} />,
      label: "None",
    },
    "Source-1": {
      icon: <PersonIcon style={{ fontSize: 20 }} />,
      label: "Direct",
    },
    "Source-2": {
      icon: <WhatsAppIcon style={{ fontSize: 20, color: "#25D366" }} />,
      label: "WhatsApp",
    },
    "Source-3": {
      icon: <CloudUploadIcon style={{ fontSize: 20 }} />,
      label: "Leadrat",
    },
    "Source-4": {
      icon: <FileIcon style={{ fontSize: 20 }} />,
      label: "Excell",
    },
  };

const GetIconComponent = ({
  column,
  option,
}: GetIconProps): React.ReactNode => {
  let key = option;

  // Handle main filter options
  if (["1", "2", "3", "4", "5", "6"].includes(option)) {
    key = option;
  }
  // Handle status options
  else if (
    column === "Category" ||
    column === "Status" ||
    column === "Chat Status" ||
    column === "Source"
  ) {
    key = `${column.replace(/\s/g, "")}-${option}`;
  }

  const { icon } = DEFAULT_ICONS[key] || { icon: null, label: "" };

  return icon ? (
    <span style={{ display: "flex", alignItems: "center" }}>{icon}</span>
  ) : null;
};

export default GetIconComponent;
