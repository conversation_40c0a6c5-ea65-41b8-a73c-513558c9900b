import React, { useState } from "react";
import { Box, Grid, IconButton } from "@mui/material";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { makeStyles } from "@mui/styles";
import ChatSideBar from "../../components/InboxComponents/ChatSideBar";
import InboxDetails from "../../components/InboxComponents/InboxDetails";
import { useEffect } from "react";
import LoadingComponent from "../../components/common/LoadingComponent";
import { bgColors } from "../../utils/bgColors";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import {
  getExpectedWalletDetails,
  getWalletDetails,
} from "../../redux/slices/Wallet/WalletSlice";
import NoAccessPage from "../../components/common/NoAccess";
import RightArrowSvg from "../../assets/svgs/RightArrowSvg";
import PersistentDrawerLeft from "../../components/InboxComponents/chatSideBarComponents/ChatDrawer";

const useStyles = makeStyles({
  sideBar: {
    position: "sticky",
    top: 0,
    height: "100vh",
    overflowY: "hidden",
    boxShadow: "1px 2px 4px #CDCDCD",
  },
  outlet: {
    overflowY: "auto",
    height: "100vh",
  },
});

const Inbox = ({
  contactsListLoading,
  setContactsListLoading,
  connection,
  contacts,
  setContacts,
  messages,
  chat,
  filterData,
  chatsPageNumber,
  setChatsPageNumber,
  setMessages,
  searchInput,
  setSearchInput,
  setFilterData,
  setContactNumber,
  contactNumber,
  handleLoadMore,
  handleLatestInboxContacts,
  setTotalUnreads,
  dateRangeFilter,
  setDateRangeFilter,
  refreshContactsList,
}: any) => {
  const classes = useStyles();
  const location = useLocation();
  const params = useParams();
  const dispatch = useAppDispatch();
  const [open, setOpen] = useState(true);
  const isInbox = location.pathname.startsWith("/inbox");
  const loginData = useAppSelector((state: any) => state?.adminLogin?.data);
  const expectedWalletSlice = useAppSelector((state: any) => state?.wallet);
  const currentPlanDetails = useAppSelector(
    (state: any) => state?.wallet?.walletAndSubscription?.data
  );
  const accountData = useAppSelector((state: any) => state?.accountData?.data);
  const walletBalance =
    expectedWalletSlice?.expectedAmount?.data?.expectedWalletBalance;

  useEffect(() => {
    dispatch(getWalletDetails());
    dispatch(getExpectedWalletDetails(loginData?.companyId));
    setContactNumber(params?.id);
  }, []);

  useEffect(() => {
    if (searchInput) {
      setContactNumber("help");
    }
  }, [searchInput]);

  const handleDrawerOpen = () => {
    setOpen(true);
  };

  const handleCloseDrawer = () => {
    setOpen(false);
  };

  useEffect(() => {
    if (loginData?.userId) {
      if (chatsPageNumber === 1) {
        setContactsListLoading({
          allContactsList: true,
          paginatedContactsList: false,
        });
      }

      // Debounce function
      const handler = setTimeout(() => {
        handleLatestInboxContacts();
      }, 500);

      return () => clearTimeout(handler);
    }
  }, []);

  return (
    <>
      {currentPlanDetails?.subscriptionPlan?.isActive &&
      ((accountData?.companyVerificationStatus === true &&
        accountData?.isMetaEnabled) ||
        (accountData?.isMetaEnabled === false &&
          accountData?.companyVerificationStatus === false &&
          currentPlanDetails?.subscriptionPlan?.planName === "Intro")) ? (
        <Grid container>
          {expectedWalletSlice?.expectedWalletstatus === "loading" ? (
            <Box sx={{ height: "100vh", width: "100%" }}>
              <LoadingComponent height="100%" color={bgColors?.blue} />
            </Box>
          ) : (
            <>
              <IconButton
                color="inherit"
                aria-label="open drawer"
                onClick={handleDrawerOpen}
                edge="start"
                sx={{
                  display: { xs: "flex", md: "none" },
                  position: "absolute",
                  left: 0,
                  background: "#cdcdcd",
                }}
              >
                <RightArrowSvg />
              </IconButton>
              <PersistentDrawerLeft
                open={open}
                handleCloseDrawer={handleCloseDrawer}
                contacts={contacts}
                searchInput={searchInput}
                filterData={filterData}
                chatsPageNumber={chatsPageNumber}
                setChatsPageNumber={setChatsPageNumber}
                setSearchInput={setSearchInput}
                setFilterData={setFilterData}
                setContactNumber={setContactNumber}
                contactsListLoading={contactsListLoading}
                setContactsListLoading={setContactsListLoading}
                setContacts={setContacts}
                setTotalUnreads={setTotalUnreads}
                dateRangeFilter={dateRangeFilter}
                setDateRangeFilter={setDateRangeFilter}
                refreshContactsList={refreshContactsList}
              />
              <Grid
                item
                xs={0}
                sx={{ display: { xs: "none", md: "block" } }}
                md={3.4}
                lg={3}
                xl={2.7}
                className={classes.sideBar}
              >
                {isInbox && (
                  <ChatSideBar
                    contacts={contacts}
                    setContacts={setContacts}
                    searchInput={searchInput}
                    filterData={filterData}
                    chatsPageNumber={chatsPageNumber}
                    setChatsPageNumber={setChatsPageNumber}
                    setSearchInput={setSearchInput}
                    setFilterData={setFilterData}
                    setContactNumber={setContactNumber}
                    contactsListLoading={contactsListLoading}
                    setContactsListLoading={setContactsListLoading}
                    setTotalUnreads={setTotalUnreads}
                    dateRangeFilter={dateRangeFilter}
                    setDateRangeFilter={setDateRangeFilter}
                    refreshContactsList={refreshContactsList}
                  />
                )}
              </Grid>
              <Grid
                item
                xs={12}
                md={8.6}
                lg={9}
                xl={9.3}
                className={classes.outlet}
              >
                <InboxDetails
                  walletBalance={walletBalance}
                  messages={messages}
                  setMessages={setMessages}
                  chat={chat}
                  contactNumber={contactNumber}
                  contacts={contacts}
                  setContacts={setContacts}
                  hubConnection={connection}
                  onLoadMore={handleLoadMore}
                  setChatsPageNumber={setChatsPageNumber}
                />
              </Grid>
            </>
          )}
        </Grid>
      ) : (
        <NoAccessPage />
      )}
    </>
  );
};

export default Inbox;
