import React from "react";
import { Box, Typography, Divider, useMediaQuery } from "@mui/material";
import { useAppSelector } from "../../utils/redux-hooks";

const WalletBalanceDisplay = () => {
  const wallet = useAppSelector((state: any) => state?.wallet);
  const walletBalance = wallet?.walletAndSubscription?.data?.walletBalance ?? 0;
  const expectedBalance =
    wallet?.expectedAmount?.data?.expectedWalletBalance ?? 0;
  const isSmallScreen = useMediaQuery("(max-width:600px)");

  if (isSmallScreen) {
    // Stack vertically for small screens
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 1,
          width: "100%",
          px: 2,
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Typography
            variant="caption"
            sx={{
              color: "#2e7d32",
              fontWeight: 600,
              letterSpacing: 0.5,
              mb: 0.5,
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            Wallet Balance
          </Typography>
          <Typography
            variant="h6"
            sx={{ color: "#222", fontWeight: 700, fontSize: 16 }}
          >
            Rs {walletBalance}/-
          </Typography>
        </Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Typography
            variant="caption"
            sx={{
            color: "#2e7d32",
              fontWeight: 600,
              letterSpacing: 0.5,
              mb: 0.5,
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            Expected Balance
          </Typography>
          <Typography
            variant="h6"
            sx={{ color: "#222", fontWeight: 700, fontSize: 16 }}
          >
            Rs {expectedBalance}/-
          </Typography>
        </Box>
      </Box>
    );
  }

  // Horizontal layout for larger screens
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        minWidth: 260,
        gap: 3,
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          flex: 1,
        }}
      >
        <Typography
          variant="caption"
          sx={{
            color: "#2e7d32",
            fontWeight: 600,
            letterSpacing: 0.5,
            mb: 0.5,
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          Wallet Balance
        </Typography>
        <Typography
          variant="h6"
          sx={{ color: "#222", fontWeight: 700, fontSize: 16 }}
        >
          Rs {walletBalance}/-
        </Typography>
      </Box>
      <Divider
        orientation="vertical"
        flexItem
        sx={{ mx: 2, borderColor: "#E0E0E0" }}
      />
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          flex: 1,
        }}
      >
        <Typography
          variant="caption"
          sx={{
            color: "#2e7d32",
            fontWeight: 600,
            letterSpacing: 0.5,
            mb: 0.5,
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          Expected Balance
        </Typography>
        <Typography
          variant="h6"
          sx={{ color: "#222", fontWeight: 700, fontSize: 16 }}
        >
          Rs {expectedBalance}/-
        </Typography>
      </Box>
    </Box>
  );
};

export default WalletBalanceDisplay;
