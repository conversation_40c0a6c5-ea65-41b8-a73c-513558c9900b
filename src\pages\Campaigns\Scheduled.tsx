import {
  Box,
  Card,
  CardContent,
  Chip,
  Grid,
  Icon<PERSON>utton,
  Tooltip,
  Typography,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import React, { useCallback, useEffect, useState } from "react";
import { bgColors } from "../../utils/bgColors";
import DeletePopUp from "../../components/common/DeletePopup";
import EditIconSvg from "../../assets/svgs/EditIconSvg";
import DeleteIconSvg from "../../assets/svgs/DeleteIconSvg";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { CAMPAIGN_API } from "../../Apis/Campaign/Campaign";
import { toastActions } from "../../utils/toastSlice";
import { checkSceduledCampaignsPermission } from "../../utils/permissions";
import NoAccessPage from "../../components/common/NoAccess";
import { getCampaignById } from "../../redux/slices/Campaign/GetCampaignByIdSlice";
import { CampaignStatusEnum } from "./Campaigns";
import CommonTable, { TableColumn } from "../../components/common/CommonTable";
import { Add } from "@mui/icons-material";
import { debounce } from "lodash";
import EditCampaign from "../../components/ScheduledComponents/EditCampaign";
import moment from "moment";
import {
  campaignAllFilters,
  campaignAllFiltersActions,
} from "../../redux/slices/Campaign/CampaignAllFiltersSlice";

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white1,
    height: "100vh",
    width: "100%",
    overFlow: "hidden !important",
  },
  searchField: {
    width: "90%",
    backgroundColor: "white",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "inter",
      color: "#000000 !important",
    },
  },
  bgContainer: {
    backgroundColor: bgColors.white,

    height: "100%",
    width: "100%",
    overFlow: "hidden !important",
    display: "flex",
    flexDirection: "column",
  },
  manageTeamContainer: {
    display: "flex",

    width: "full",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    // fontWeight: "600 !important",
  },
  SaveChangesButton: {
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    // backgroundColor: bgColors.green,
    borderRadius: "8px",
    width: "120px",
    height: "32px",
    // padding: "10px",
    cursor: "pointer",
  },
  messageInnerContainer: {
    border: "2px solid #F2F2F2",
    borderRadius: "6px",
    paddingInline: "4px",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  grayColor: {
    color: "#303030",
    opacity: "60%",
    fontSize: "20px",
    // padding:"5px"
  },
  spaceBetween: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  iconStyles: {
    cursor: "pointer",
    paddingLeft: "5px",
  },
  table: {
    minWidth: "500px",

    overflow: "auto",
    // borderCollapse: "separate",
    // borderSpacing: "0",
    textAlign: "center",
    borderColor: "lightgray",
    "& th, & td": {
      // borderTop: '1px solid gray',
      borderBottom: "1px solid #f0f0f0",
      height: "35.8px",
    },
    "& th:first-child, & td:first-child": {
      borderLeft: "none",
    },
    "& th:last-child, & td:last-child": {
      borderRight: "none",
    },
  },
  teamProfileContainer: {
    display: "flex",
    alignItems: "center",
  },
  threedots: {
    border: "2px solid #F2F2F2",
    padding: "10px",
    borderRadius: "12px",
    // paddingBottom: 0,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  editButtonContainer: {
    border: "2px solid #DBDBDB",
    padding: "8px",
    borderRadius: "12px",
    backgroundColor: "#F4F4F4",
    width: "50px",
    // paddingBottom: 0,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    cursor: "pointer",
  },
});

const Scheduled = () => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const userInfoSlice = useAppSelector((state: any) => state.adminLogin);
  const accountInfo = useAppSelector((state: any) => state?.accountData?.data);
  const userInfo = userInfoSlice?.data;
  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const getMc = getuserPermissionData?.campaigns;
  const scheduledObject = getMc?.find((item: any) =>
    Object?.prototype?.hasOwnProperty?.call(item, "scheduled")
  );
  const scheduledActions = scheduledObject ? scheduledObject.scheduled : [];
  const state = useAppSelector((state: any) => state);
  const campaignAllFiltersData =
    state?.campaignAllFiltersData?.data?.data || [];
  const campaignAllFiltersStatus =
    state?.campaignAllFiltersData?.status || "idle";
  const hasScheduledCampaignsPermission =
    checkSceduledCampaignsPermission(getMc);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [searchCampaignQuery, setSearchCampaignQuery] = useState<string>("");

  const [page, setPage] = React.useState(1);

  const [openEditDialog, setOpenEditDialog] = React.useState(false);

  const [deleteDialog, setDeleteDialog] = React.useState(false);
  const [title, setTitle] = useState("");
  const [addNewCampaignTooltip, setAddNewCampaignTooltip] = useState(false);
  const [editScheduledCampaignTooltip, setEditScheduledCampaignTooltip] =
    useState("");

  const [deleteScheduledCampaignTooltip, setDeleteScheduledCampaignTooltip] =
    useState("");
  const [scheduledCampaignTooltipMesage, setScheduledCampaignTooltipMesage] =
    useState("");
  const [editData, setEditData] = useState<any>(null);
  const [getCampaignId, setGetCampaignId] = useState<any>(null);
  const [pageData, setPageData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [perPage, setPerPage] = useState(10);

  // Column filter states
  const [columnFilters, setColumnFilters] = useState<Record<string, string>>(
    {}
  );
  const [columnFilterLoading, setColumnFilterLoading] = useState<
    Record<string, boolean>
  >({});

  // Clear data when component mounts to ensure fresh loading state
  useEffect(() => {
    dispatch(campaignAllFiltersActions.clearData());

    // Clear data when component unmounts to prevent data leakage
    return () => {
      dispatch(campaignAllFiltersActions.clearData());
    };
  }, [dispatch]);

  const hasAcess = (permission: any) => {
    if (scheduledActions?.includes(permission)) {
      return true;
    }
    return false;
  };

  const debouncedFetchCampaignData = useCallback(
    debounce(async (data) => {
      const res = await dispatch(campaignAllFilters(data));
    }, 500),
    []
  );

  const handleDeleteDialog = (campaignId: string, createdBy: string) => {
    const hasPermission =
      userInfo?.roleName === "Owner" || userInfo?.roleName === "Admin"
        ? hasAcess("deleteCampaign")
        : hasAcess("deleteCampaign") && createdBy === accountInfo?.name;
    if (hasPermission) {
      setDeleteDialog(true);
      setGetCampaignId(campaignId);
    } else {
      if (createdBy !== accountInfo?.name) {
        setScheduledCampaignTooltipMesage("You are not authorized");
      } else {
        setScheduledCampaignTooltipMesage("Access Denied");
      }
      setDeleteScheduledCampaignTooltip(campaignId);
      setTimeout(() => {
        setDeleteScheduledCampaignTooltip("");
      }, 2000);
    }
  };

  const handleDeleteCloseDialog = () => {
    setDeleteDialog(false);
  };

  const handleOpenEditDialog = (title: any) => {
    const hasPermission = hasAcess("newScheduledCampaign");
    setTitle(title);
    if (hasPermission) {
      setOpenEditDialog(true);
    } else {
      setAddNewCampaignTooltip(true);
      setTimeout(() => {
        setAddNewCampaignTooltip(false);
      }, 2000);
    }
  };
  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
    setEditData(null);
  };
  const handleEdit = async (client: any, id: any, createdBy: string) => {
    const hasPermission =
      userInfo?.roleName === "Owner" || userInfo?.roleName === "Admin"
        ? hasAcess("editCampaign")
        : hasAcess("editCampaign") && createdBy === accountInfo?.name;
    if (hasPermission) {
      handleOpenEditDialog(client);
      try {
        const data = {
          businessId: userInfo?.companyId,
          userId: userInfo?.userId,
          campaignId: id,
        };

        const getDetailsRes = await dispatch(getCampaignById(data));

        setEditData(getDetailsRes?.payload?.data);
      } catch (error: any) {}
    } else {
      if (createdBy !== accountInfo?.name) {
        setScheduledCampaignTooltipMesage("You are not authorized");
      } else {
        setScheduledCampaignTooltipMesage("Access Denied");
      }
      setEditScheduledCampaignTooltip(id);
      setTimeout(() => {
        setEditScheduledCampaignTooltip("");
      }, 2000);
    }
  };

  const handleDelete = async () => {
    setIsDeleteLoading(true);
    try {
      const data = {
        businessId: userInfo?.companyId,
        userId: userInfo?.userId,
        campaignId: getCampaignId,
      };
      const getDeleteRes = await CAMPAIGN_API.deleteScheduleCampaign(data);
      if (getDeleteRes?.status === 200) {
        setDeleteDialog(false);
        fetchCampaignData(page);
        dispatch(
          toastActions.setToaster({
            type: "success",
            message: `${getDeleteRes?.data?.message}`,
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: `${getDeleteRes?.data?.message}`,
          })
        );
        setDeleteDialog(false);
      }
    } catch (error) {}
    setIsDeleteLoading(false);
  };
  const handlePageChange = (event: any, value: number) => {
    setPage(value);
  };

  const handleColumnFilter = (columnId: string, selectedValue: string) => {
    // Set loading for this specific column
    setColumnFilterLoading((prev) => ({
      ...prev,
      [columnId]: true,
    }));
    setColumnFilters((prev) => ({
      ...prev,
      [columnId]: selectedValue,
    }));
    setPage(1);
  };

  const handleClearColumnFilter = (columnId: string) => {
    // Set loading for this specific column
    setColumnFilterLoading((prev) => ({
      ...prev,
      [columnId]: true,
    }));
    setColumnFilters((prev) => {
      const newFilters = { ...prev };
      delete newFilters[columnId];
      return newFilters;
    });
    setPage(1);
  };

  const fetchCampaignData = useCallback(
    (pageNumber = page) => {
      // Build date range filters
      const dateRangeFilters = [];
      if (columnFilters.dateSetLive) {
        try {
          const dateRange = JSON.parse(columnFilters.dateSetLive);
          if (dateRange.startDate && dateRange.endDate) {
            dateRangeFilters.push({
              column: "DateSetLive",
              fromDate: moment(`${dateRange.startDate}T00:00:00Z`)
                .utc()
                .format(),
              toDate: moment(`${dateRange.endDate}T00:00:00Z`).utc().format(),
            });
          }
        } catch {}
      }

      // Only the filter/search/sort/dateRange objects in the body
      const filters = {
        searching: {
          value: searchCampaignQuery?.length > 0 ? searchCampaignQuery : "",
        },
        sorting: {
          column: "",
          order: "",
        },
        filtering: {
          filterType: "and",
          conditions: [
            {
              column: "State",
              operator: "equals",
              value: "Scheduled",
            },
          ],
        },
        dateRangeFilters: dateRangeFilters,
      };
      debouncedFetchCampaignData({
        filters,
        page: pageNumber,
        perPage,
        businessId: userInfo?.companyId,
        userId: userInfo?.userId,
      });
    },
    [
      searchCampaignQuery,
      perPage,
      columnFilters,
      debouncedFetchCampaignData,
      userInfo?.companyId,
      userInfo?.userId,
    ]
  );

  useEffect(() => {
    fetchCampaignData(page);
  }, [searchCampaignQuery, page, columnFilters, fetchCampaignData]);

  const formatDate = (datetime: any) => {
    const date = new Date(datetime);
    // Convert UTC to IST by adding 5 hours and 30 minutes
    date.setHours(date.getHours() + 5);
    date.setMinutes(date.getMinutes() + 30);

    const year = date.getFullYear();
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[date.getMonth()];
    const day = ("0" + date.getDate()).slice(-2);
    const hours = ("0" + date.getHours()).slice(-2);
    const minutes = ("0" + date.getMinutes()).slice(-2);
    const seconds = ("0" + date.getSeconds()).slice(-2);
    return `${day} ${month} ${year} ${hours}:${minutes}:${seconds}`;
  };

  useEffect(() => {
    setPageData(campaignAllFiltersData);
    // Set totalCount from the payload if available
    const count = state?.campaignAllFiltersData?.data?.total;
    setTotalCount(count || 0);
  }, [campaignAllFiltersData, state?.campaignAllFiltersData?.data?.total]);

  // Clear column filter loading states when API call completes
  useEffect(() => {
    if (
      campaignAllFiltersStatus === "succeeded" ||
      campaignAllFiltersStatus === "failed"
    ) {
      setColumnFilterLoading({});
    }
  }, [campaignAllFiltersStatus]);

  // Define the columns for the Scheduled campaigns table
  const columns: TableColumn[] = [
    {
      id: "campaignTitle",
      label: "Title",
      align: "left",
      format: (value: any) => (
        <Tooltip title={value} placement="top">
          <Box
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              maxWidth: "250px",
              paddingLeft: "20px",
            }}
          >
            {value}
          </Box>
        </Tooltip>
      ),
    },
    {
      id: "createdby",
      label: "Created by",
    },
    {
      id: "state",
      label: "State",
      format: (value) => {
        const stateMap = {
          1: { label: "Completed", color: "success" },
          2: { label: "Incomplete", color: "error" },
          3: { label: "Scheduled", color: "warning" },
        };
        const state = stateMap[value as keyof typeof stateMap];
        return (
          <Chip
            label={state?.label || ""}
            color={state?.color as any}
            size="small"
            sx={{
              background:
                stateMap[value as keyof typeof stateMap]?.color === "success"
                  ? "#D1FADF"
                  : stateMap[value as keyof typeof stateMap]?.color === "error"
                  ? "#FEF2F2"
                  : "#FFFBEB",
              color:
                stateMap[value as keyof typeof stateMap]?.color === "success"
                  ? "#039855"
                  : stateMap[value as keyof typeof stateMap]?.color === "error"
                  ? "#DC2626"
                  : "#DC2626",
            }}
          />
        );
      },
    },
    {
      id: "dateSetLive",
      label: "Date Set Live",
      filterable: true,
      filterOptions: [], // Will be handled by date filter
      format: (value: any) => formatDate(value),
    },
  ];

  // Define the actions renderer for the table
  const renderActions = (row: any) => {
    return (
      <Box
        sx={{
          display: "flex",
          gap: 1,
          justifyContent: "flex-start",
          alignItems: "center",
        }}
      >
        {row?.state === 3 && (
          <Tooltip title="Edit">
            <Box
              onClick={() =>
                handleEdit("Edit", row?.campaignId, row?.createdby)
              }
              sx={{
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
              }}
            >
              <EditIconSvg />
            </Box>
          </Tooltip>
        )}
        <Tooltip title="Delete">
          <Box
            onClick={() => handleDeleteDialog(row?.campaignId, row?.createdby)}
            sx={{
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
            }}
          >
            <DeleteIconSvg />
          </Box>
        </Tooltip>
      </Box>
    );
  };

  // Define the mobile renderer
  const renderScheduledMobile = (row: any) => {
    return (
      <Card sx={{ m: 1, p: 2 }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}>
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              maxWidth: "200px",
            }}
          >
            {row.campaignTitle}
          </Typography>
          <Box sx={{ display: "flex", gap: 1 }}>
            {row?.state === 3 && (
              <IconButton
                size="small"
                onClick={() =>
                  handleEdit("Edit", row?.campaignId, row?.createdby)
                }
              >
                <EditIconSvg />
              </IconButton>
            )}
            <IconButton
              size="small"
              onClick={() =>
                handleDeleteDialog(row?.campaignId, row?.createdby)
              }
            >
              <DeleteIconSvg />
            </IconButton>
          </Box>
        </Box>

        <CardContent sx={{ pt: 0, pb: "8px !important" }}>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="caption" color="text.secondary">
                Created by
              </Typography>
              <Typography variant="body2">{row.createdby}</Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="caption" color="text.secondary">
                State
              </Typography>
              <Typography variant="body2">
                {row.state === 3
                  ? "Scheduled"
                  : row.state === 1
                  ? "Completed"
                  : "Incomplete"}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="caption" color="text.secondary">
                Date Set Live
              </Typography>
              <Typography variant="body2">
                {formatDate(row.dateSetLive)}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  };

  return (
    <>
      {hasScheduledCampaignsPermission ? (
        <Grid sx={{ height: "100%" }}>
          <Grid className={classes.mainContainer}>
            <CommonTable
              columns={columns}
              data={pageData}
              rowIdKey="campaignId"
              title="Scheduled Campaigns"
              count={totalCount}
              isLoading={campaignAllFiltersStatus === "loading"}
              actions={renderActions}
              renderOnMobile={renderScheduledMobile}
              showPagination={true}
              page={page}
              onPageChange={handlePageChange}
              totalPages={Math.ceil(totalCount / perPage)}
              searchProps={{
                value: searchCampaignQuery,
                onChange: setSearchCampaignQuery,
                placeholder: "Search scheduled campaigns...",
              }}
              primaryAction={{
                label: "Add Campaign",
                onClick: () => handleOpenEditDialog("Add"),
                icon: <Add />,
                show: hasAcess("newScheduledCampaign"),
              }}
              perPage={perPage}
              perPageOptions={[10, 25, 50, 100, 200, 500]}
              onPerPageChange={(value) => {
                setPerPage(value);
                setPage(1);
              }}
              onColumnFilter={handleColumnFilter}
              onClearColumnFilter={handleClearColumnFilter}
              columnFilters={columnFilters}
              columnFilterLoading={columnFilterLoading}
              useScheduledDateFilter={true}
              status={campaignAllFiltersStatus}
            />
          </Grid>
          <DeletePopUp
            title="Schedule Campaign"
            open={deleteDialog}
            handleClose={handleDeleteCloseDialog}
            handleDelete={handleDelete}
            handleLoad={isDeleteLoading}
          />
          <EditCampaign
            open={openEditDialog}
            title={title}
            handleClose={handleCloseEditDialog}
            data={title === "Edit" ? editData : null}
            searchCampaignQuery={searchCampaignQuery}
          />
        </Grid>
      ) : (
        <NoAccessPage />
      )}
    </>
  );
};

export default Scheduled;
