import { useEffect, useMemo, useRef, useState } from "react";
import moment from "moment";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  InputLabel,
  Grid,
  FormControl,
  Select,
  MenuItem,
  InputAdornment,
  FormControlLabel,
  Switch,
  IconButton,
  Popover,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import CloseSvg from "../../assets/svgs/CloseSvg";
import { makeStyles } from "@mui/styles";
import TextFeildWithBorderComponet from "../common/TextFieldWithBorderComponent";
import InfoIcon from "@mui/icons-material/Info";

import { bgColors } from "../../utils/bgColors";
import { LocalizationProvider, TimePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import dayjs from "dayjs";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { createCampaign } from "../../redux/slices/Campaign/CreateCampaignSlice";
import EmojiPopover from "../InboxComponents/inboxDetailsComponents/EmojiPicker";
import CloseIconSvg from "../../assets/svgs/CloseIconSvg";
import { LoadingButton } from "@mui/lab";
import LoadingComponent from "../common/LoadingComponent";
import "moment-timezone";
import TemplatePreviewLayout from "../TemplateComponents/TemplateForm/templatePreviewLayout";
import CampaignIcon from "@mui/icons-material/Campaign";
import { getCampaignTitle } from "../../redux/slices/Campaign/GetCampaignTitleSlice";
import useDebouncedFetch from "../../utils/debounceHook";
import { getExpectedWalletDetails } from "../../redux/slices/Wallet/WalletSlice";
import { getCurrentDate } from "../../utils/functions";
import CommonAccordion from "../common/CommonAccordion";
import WorkFlowPopUpCampaign from "./WorkFlowPopUpCampiagn";
import CustomReplyPopUpCampiagn from "./CustomReplyPopUpCampiagn";
import { validateForm } from "../../utils/validationUtils";
import { createAutoReplyMessage } from "../../redux/slices/Campaign/CreateAutoReplyMessageSlice";
import { debounce } from "lodash";
import { CampaignStatusEnum } from "../../pages/Campaigns/Campaigns";
import ContactSelectionOption from "./ContactSelectionOption";
import { getFileByBlobId } from "../../redux/slices/Campaign/GetFileByBlobId";
import { fetchAllContacts } from "../../redux/slices/Contacts/AllContactsSlice";
import { rerunCampaign } from "../../redux/slices/Campaign/RerunCampaignSlice";
import { toastActions } from "../../utils/toastSlice";
import Radio from "@mui/material/Radio";
import { useNavigate } from "react-router-dom";

export enum ResponseType {
  CustomMessage = 1,
  Multiproduct = 2,
  Workflow = 3,
  ProductCollectionList = 4,
}
export enum CustomerResponse {
  None = 0,
  OnButtonClick = 1,
  TypedOutReply = 2,
  AnyCustomerResponse = 3,
}

const useStyles = makeStyles({
  popoverContainer: {
    padding: "16px",
  },
  greenButton: {
    color: "#1976d2",
    fontWeight: "700",
    borderRadius: 12,
    paddingInline: "6px",
  },
  yellowButton: {
    color: "#ff9800",
    fontWeight: "700",
    borderRadius: 12,
    paddingInline: "6px",
  },
  redButton: {
    // backgroundColor: bgColors.red2,
    color: bgColors.red1,
    fontWeight: "700",
    borderRadius: 12,
    paddingInline: "6px",
  },
  darkGreenButton: {
    // backgroundColor: bgColors.green,
    color: "#4caf50",
    fontWeight: "700",
    borderRadius: 12,
    paddingInline: "6px",
  },
  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "16px",
  },
  title: {
    color: bgColors.black1,
    fontWeight: "normal",
  },
  closeButton: {
    color: bgColors.black1,
  },
  selectContainer: {
    marginBottom: "16px",
  },
  textColor: {
    color: "#3C3C3C",
    fontWeight: "500",
  },
  changePasswordContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    // cursor: "pointer",
  },
  updateButtonStyles: {
    backgroundColor: "#3C3C3C",
    color: "#ffffff",
    height: "40px",
    borderRadius: "8px",
    width: "100%",
  },
  blackColor: {
    color: "#303030 !important",
    fontWeight: "500 !important",
    marginBottom: "8px",
  },
  button: {
    backgroundColor: `#3C3C3C !important`,
    width: "100% !important",
    fontSize: "24px !important",
    fontWeight: "Semi Bold !important",
    borderRadius: "8px !important",
  },
  messageCountContainer: {
    border: "1px solid #cdcdcd",
    borderRadius: "12px",
    padding: "8px",
    // width: "10px",
    paddingBottom: 6,
    justifyContent: "center",
    alignItems: "center",
  },
  messageInnerContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    // alignItems: "center",
  },
  messageCount: {
    backgroundColor: "#DBDBDB",
    borderRadius: "24px",
    padding: "3px",
    width: "40px",
    // color: "white",
    textAlign: "center",
  },
  grayColor: {
    color: "#4B5A5A !important",
    opacity: "60%",
    // padding:"5px"
  },
  iconStyles: {
    cursor: "pointer",
    // paddingLeft: "5px",
    // marginLeft:'385px'
  },
  cursor: {
    cursor: "pointer",
    fontSize: "10px",
  },
  buttonContainer: {
    display: "flex",
    justifyContent: "space-between",
  },
  resetBtnStyles: {
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    width: "100px",
    height: "38px",
    padding: "5px",
    cursor: "pointer",
    backgroundColor: "#fff",
    "&:hover": {
      backgroundColor: "rgba(68, 71, 70, 0.08)",
    },
  },
  SaveChangesButton: {
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    width: "130px",
    height: "38px",
    padding: "5px",
    cursor: "pointer",
    backgroundColor: "#fff",
    "&:hover": {
      backgroundColor: "rgba(68, 71, 70, 0.08)",
    },
  },
  SelctAlloption: {
    borderBottom: "1px solid rgba(0, 0, 0, 0.12) !important",
    // display: "flex !important",
    // flexDirection: "row",
    // justifyContent: "space-between !important",
    // alignItems: "center",
    // padding: "5px !important",
  },
  SelctAlloptionText: {
    display: "flex !important",
    flexDirection: "row",
    justifyContent: "space-between !important",
    alignItems: "center !important",
    width: "100% !important",
  },
  account: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    padding: "5px",
  },
  switch: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    fontSize: "16px !important",
    color: `#3C3C3C !important`,
  },
  textField: {
    height: "10px !important",
  },
  dateField: {
    width: "135px",
    // '& .MuiInputBase-input[type="date"]': {
    //   padding: "15px",
    // },
    // "& .MuiIconButton-root": {
    //   marginLeft: "-8px",
    // },
  },
  timefield: {
    ".css-jv54yp-MuiList-root-MuiMultiSectionDigitalClockSection-root": {
      scrollBehavior: "auto",
      scrollbarWidth: "none !important",
    },

    ".css-1st8yxe-MuiPickersLayout-root .MuiPickersLayout-actionBar": {
      display: "none",
    },
  },
});

interface CampaignState {
  name?: string;
  autoCustomAutomation: {
    input: string;
    responseType: ResponseType;
    customerResponse?: CustomerResponse;
    bodyMessage: string;
  } | null;
  workflowAutomation: {
    input: string;
    responseType: number;
    customerResponse?: CustomerResponse;
    workflowName: any;
    contactIds?: string[];
  } | null;
}

const ResendCampaignPopup = ({
  campaignDetails,
  open,
  handleClose,
  totalContacts = 0,
  failedContacts = 0,
  undeliveredContacts = 0,
  rerunType = "failed",
  setRerunType = () => {},
  campaignAnalyticsDetails,
  onSuccess,
}: any) => {
  const classes = useStyles();
  const navigate = useNavigate();
  const rerunCampaignSlice = useAppSelector(
    (state: any) => state.rerunCampaign
  );

  const [searchContactQuery, setSearchContactQuery] = useState<string>("");

  // states for handling popup
  const [outerDialogOpen, setOuterDialogOpen] = useState(true);

  const [isSwitchChecked, setSwitchChecked] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string | null>(
    getCurrentDate()
  );
  const [selectedTime, setSelectedTime] = useState<dayjs.Dayjs | null>(dayjs());

  const [isResendLoading, setIsResendLoading] = useState(false);

  const [titleSearch, setTitleSearch] = useState("");

  const [formTouched, setFormTouched] = useState(false);

  const outerDialogRef = useRef<HTMLDivElement>(null);

  //Slice and redux functionality
  const dispatch = useAppDispatch();
  const debouncedFetchCampaigns = useDebouncedFetch(getCampaignTitle, 500);
  const userInfoSlice = useAppSelector((state: any) => state.adminLogin);
  const userInfo = userInfoSlice?.data;

  const rerunCampaignStatus = useAppSelector(
    (state: any) => state.rerunCampaign?.status
  );

  const searchResult = useAppSelector(
    (state: any) => state?.getCampaignTitle?.data?.success
  );

  const searchStatus = useAppSelector(
    (state: any) => state?.getCampaignTitle?.status
  );

  const [formErrors, setFormErrors] = useState<{ name?: string }>({});
  const [localCampaignTitle, setLocalCampaignTitle] = useState(
    campaignDetails?.campaignTitle || ""
  );

  const [scheduleError, setScheduleError] = useState<string | null>(null);

  // handler functions

  const handleClickOutside = (event: MouseEvent) => {
    if (
      outerDialogRef.current &&
      !outerDialogRef.current.contains(event.target as Node)
    ) {
      setOuterDialogOpen(false);
    }
  };

  const handleCloseAll = () => {
    setSwitchChecked(false);
    setFormTouched(false);
    setSearchContactQuery("");
    handleClose();
  };

  const handleDateChange = (event: any) => {
    const newDate = event.target.value;
    setSelectedDate(newDate);
    // setFormData({ ...formData, selectedDate: newDate });
  };

  const handleTimeChange = (newValue: any) => {
    setSelectedTime(newValue);
    // setFormData({ ...formData, setSelectedTime: newValue });
  };

  const isValidForm = () => {
    let isValid = true;
    const errors: { name?: string } = {};
    setScheduleError(null);

    if (!localCampaignTitle?.trim()) {
      errors.name = "Campaign title is required";
      isValid = false;
    } else if (localCampaignTitle === campaignDetails?.campaignTitle) {
      errors.name =
        "Campaign title for rerun must be different from the current campaign title.";
      isValid = false;
    } else if (
      localCampaignTitle !== campaignDetails?.campaignTitle &&
      searchResult === true
    ) {
      errors.name = "Campaign name already exists.";
      isValid = false;
    }

    // Schedule date/time validation
    if (isSwitchChecked && selectedDate && selectedTime) {
      const selectedDateTime = moment(
        `${selectedDate}T${selectedTime.format("HH:mm")}`
      );
      const now = moment();
      if (selectedDateTime.isBefore(now)) {
        setScheduleError("Scheduled date and time must be in the future.");
        isValid = false;
      }
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleResendCampaign = async () => {
    setIsResendLoading(true);
    if (!isValidForm()) {
      setIsResendLoading(false);
      return;
    }
    try {
      if (isSwitchChecked && selectedDate && selectedTime) {
        const selectedDateTime = moment(
          `${selectedDate}T${selectedTime?.format("HH:mm")}`
        );
        const currentDateTime = moment();

        if (selectedDateTime.isBefore(currentDateTime)) {
          dispatch(
            toastActions.setToaster({
              type: "error",
              message:
                "Campaign can only be scheduled after the current date and time.",
            })
          );
          setIsResendLoading(false);
          return;
        }
      }

      // Use the same UTC conversion logic as in EditCampaign.tsx
      const dateTime = moment(
        `${selectedDate}T${
          typeof selectedTime !== "string" && selectedTime?.format("HH:mm")
        }`
      )
        .utc()
        .format();

      const payload = {
        scheduledDate: isSwitchChecked ? dateTime : null,
        data: {
          id: campaignDetails?.campaignId,
          businessId: userInfo?.companyId,
          name: localCampaignTitle,
        },
      };

      const response = await dispatch(rerunCampaign(payload));
      if (response.meta.requestStatus === "fulfilled") {
        handleCloseAll();
        setIsResendLoading(false);
        dispatch(
          toastActions.setToaster({
            message:
              response?.payload?.data?.message || "Campaign rerun successfull",
            type: "success",
          })
        );
        // Call the onSuccess function to refresh the campaign data
        onSuccess && onSuccess();
        navigate("/campaigns/one-time");
      } else {
        dispatch(
          toastActions.setToaster({
            message:
              response?.payload?.data?.message || "Failed to rerun campaign",
            type: "error",
          })
        );
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsResendLoading(false);
    }
  };

  const handleSwitchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSwitchChecked(event.target.checked);
    if (event.target.checked) {
      // setFormData({
      //   ...formData,
      //   selectedDate: getCurrentDate(),
      //   setSelectedTime: dayjs(),
      // });
    } else {
      // setFormData({ ...formData, selectedDate: null, setSelectedTime: null });
    }
  };

  const handleTextChange = (event: any) => {
    const { name, value } = event.target;
    if (name === "name") {
      setLocalCampaignTitle(value);
      setTitleSearch(value);
      setFormTouched(true);
      setFormErrors((prev) => ({ ...prev, name: undefined }));
      if (value && value !== campaignDetails?.campaignTitle) {
        // Only check for duplicate if changed
        fetchData();
      }
    }
  };

  // all useEffects

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  const fetchData = async () => {
    const data = {
      businessId: userInfo?.companyId,
      campaignTitle: titleSearch,
    };

    await debouncedFetchCampaigns(data);
  };

  useEffect(() => {
    if (
      localCampaignTitle &&
      localCampaignTitle.trim() !== "" &&
      localCampaignTitle !== campaignDetails?.campaignTitle
    ) {
      fetchData();
    }
  }, [localCampaignTitle, campaignDetails?.campaignTitle]);

  useEffect(() => {
    if (formTouched) {
      isValidForm();
    }
  }, [localCampaignTitle, searchStatus]);

  useEffect(() => {
    setLocalCampaignTitle(campaignDetails?.campaignTitle || "");
  }, [campaignDetails?.campaignTitle]);

  return (
    <Dialog
      open={open}
      onClose={handleCloseAll}
      PaperProps={{
        style: {
          minWidth: campaignAnalyticsDetails?.failedCount === 0 ? "40%" : "70%",
          borderRadius: "20px",
        },
      }}
    >
      <DialogTitle>
        <Box m={2} mb={0} className={classes.changePasswordContainer}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography
              variant="h5"
              style={{
                color: "#000000",
                fontWeight: "600",
                fontSize: "20px",
                display: "flex",
              }}
            >
              <span style={{ marginTop: "2px" }}>
                <CampaignIcon sx={{ height: "22px" }} />
              </span>{" "}
              &nbsp; {campaignDetails?.campaignTitle} Campaign
            </Typography>
            <Tooltip
              title={
                <>
                  <div>
                    1. We can only send message and media campaigns to contacts
                    with chat status open only.
                  </div>
                  <div>
                    2. We can only send campaigns to contacts with OptIn status
                    active only.
                  </div>
                </>
              }
              arrow
            >
              <IconButton size="small">
                <InfoIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
          <Box onClick={handleCloseAll} sx={{ cursor: "pointer" }}>
            <CloseSvg />
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent>
        {campaignAnalyticsDetails?.failedCount === 0 ? (
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              p: 3,
              gap: 2,
            }}
          >
            <Typography
              variant="h6"
              sx={{
                fontWeight: "600",
                textAlign: "center",
              }}
            >
              {campaignDetails?.campaignTitle}
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: "#666",
                textAlign: "center",
                maxWidth: "400px",
              }}
            >
              There are no failed contacts for this campaign. No need to rerun
              the campaign.
            </Typography>
          </Box>
        ) : (
          <>
            <Box mb={2} mt={2}>
              <TextFeildWithBorderComponet
                label="Enter your campaign title"
                name="name"
                placeholder=""
                value={localCampaignTitle}
                error={!!formErrors.name}
                helperText={formErrors.name}
                onChange={handleTextChange}
                sx={{
                  "& input": {
                    // fontWeight: "600",
                  },
                  "& .MuiFormHelperText-root": {
                    color: formErrors.name ? "red" : undefined,
                  },
                }}
                InputProps={{
                  sx: { fontSize: 14 },
                  endAdornment: (
                    <>
                      {searchStatus === "loading" && (
                        <CircularProgress size={20} />
                      )}
                    </>
                  ),
                }}
              />
            </Box>

            {/* Campaign Analytics Section */}
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 1,
                mb: 0,
                mt: 1,
                p: 2,
                backgroundColor: "#F8F9FA",
                borderRadius: "8px",
                border: "1px solid #E9ECEF",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography variant="subtitle2" sx={{ color: "#495057" }}>
                    Total Contacts:
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 600, color: "#1976d2" }}
                  >
                    {totalContacts}
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography variant="subtitle2" sx={{ color: "#495057" }}>
                    Failed Contacts:
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 600, color: "#d32f2f" }}
                  >
                    {failedContacts}
                  </Typography>
                </Box>
                {/* <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography variant="subtitle2" sx={{ color: "#495057" }}>
                    Undelivered Contacts:
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 600, color: "#f9a825" }}
                  >
                    {undeliveredContacts}
                  </Typography>
                </Box> */}
              </Box>
            </Box>
            {/* Rerun Type Selector */}
            {/* <Box sx={{ mt: 2, mb: 2 }}>
              <Typography variant="subtitle2" sx={{ color: "#495057", mb: 1 }}>
                Rerun For:
              </Typography>
              <Box sx={{ display: "flex", gap: 2 }}>
                <FormControlLabel
                  control={
                    <Radio
                      checked={rerunType === "failed"}
                      onChange={() => setRerunType("failed")}
                      sx={{
                        color: "#bdbdbd",
                        "&.Mui-checked": {
                          color: "#22c55e", // green dot
                        },
                      }}
                    />
                  }
                  label="Failed Contacts"
                />
                <FormControlLabel
                  control={
                    <Radio
                      checked={rerunType === "undelivered"}
                      onChange={() => setRerunType("undelivered")}
                      sx={{
                        color: "#bdbdbd",
                        "&.Mui-checked": {
                          color: "#22c55e",
                        },
                      }}
                    />
                  }
                  label="Undelivered Contacts"
                />
                <FormControlLabel
                  control={
                    <Radio
                      checked={rerunType === "both"}
                      onChange={() => setRerunType("both")}
                      sx={{
                        color: "#bdbdbd",
                        "&.Mui-checked": {
                          color: "#22c55e",
                        },
                      }}
                    />
                  }
                  label="Both"
                />
              </Box>
            </Box> */}

            {/* Info Message Component */}
            <Box
              sx={{
                mt: 1,
                display: "flex",
                p: 2,
                backgroundColor: `${bgColors.gray2}`,
                borderRadius: "8px",
                border: `1px solid ${bgColors.gray2}`,
                boxShadow: "0 1px 3px rgba(0,0,0,0.05)",
              }}
            >
              <Typography
                variant="body1"
                sx={{
                  fontSize: "0.9rem",
                  color: `${bgColors.yellow}`,
                  fontWeight: 500,
                  lineHeight: 1.6,
                  fontFamily: "Inter, Roboto, Arial, sans-serif",
                  fontStyle: "italic",
                  letterSpacing: 0.1,
                }}
              >
                Resending this campaign will attempt to deliver the message to
                contacts that failed in the previous attempt.
              </Typography>
            </Box>

            <Box mt={3} className={classes.account}>
              <Box display="flex" flexDirection="row">
                <Box>
                  <Typography variant="body1" className={classes.switch}>
                    Schedule for Later
                  </Typography>
                </Box>
                <Box ml={2} style={{ marginTop: "-7px" }}>
                  <FormControlLabel
                    label=""
                    control={
                      <Switch
                        checked={isSwitchChecked}
                        onChange={handleSwitchChange}
                        color="success"
                        sx={{
                          "&.Mui-unchecked .MuiSwitch-thumb": {
                            color: "white",
                          },
                          "& .MuiSwitch-track": {
                            backgroundColor: "#3CAA93",
                          },
                          "&.Mui-checked .MuiSwitch-thumb": {
                            color: "green",
                          },
                          "&.Mui-checked + .MuiSwitch-track": {
                            backgroundColor: "green",
                          },
                        }}
                      />
                    }
                  />
                  <Box
                    ml={"-160px"}
                    mt={"20px"}
                    style={{
                      display: isSwitchChecked ? "flex" : "none",
                      flexDirection: "row",
                      gap: 20,
                    }}
                  >
                    <TextField
                      id="date"
                      label="Date"
                      type="date"
                      name="date"
                      value={selectedDate}
                      onChange={handleDateChange}
                      inputProps={{
                        min: getCurrentDate(),
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      sx={{
                        width: 145,
                        top: 7,
                        height: "35px",
                        "& .MuiInputBase-root": {
                          height: "100%",
                        },
                      }}
                    />
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DemoContainer components={["TimePicker"]}>
                        <FormControl
                          sx={{
                            height: "25%",
                            width: "140px",
                            bottom: "0px",
                          }}
                        >
                          <TimePicker
                            label="Time"
                            className={classes.timefield}
                            value={selectedTime || dayjs()}
                            onChange={handleTimeChange}
                            minutesStep={1}
                            sx={{
                              height: "35px",
                              "& .MuiInputBase-root": {
                                height: "100%",
                                overflow: "hidden",
                              },
                            }}
                          />
                        </FormControl>
                      </DemoContainer>
                    </LocalizationProvider>
                  </Box>
                </Box>
              </Box>
            </Box>
            {scheduleError && (
              <Typography color="error" variant="body2" sx={{ mt: 1 }}>
                {scheduleError}
              </Typography>
            )}
          </>
        )}
      </DialogContent>
      <DialogActions>
        <Grid item xs={12} mb={4} ml={4} mr={4}>
          {campaignAnalyticsDetails?.failedCount === 0 ? (
            <Button
              onClick={handleCloseAll}
              sx={{
                backgroundColor: `${bgColors.black} !important`,
                height: "40px",
                color: "white !important",
                width: "100% !important",
                fontSize: "14px !important",
                fontWeight: "Semi Bold !important",
                borderRadius: "8px !important",
              }}
            >
              Close
            </Button>
          ) : (
            <>
              {isResendLoading ? (
                <LoadingComponent height="auto" color={bgColors?.blue} />
              ) : (
                <LoadingButton
                  loading={isResendLoading}
                  onClick={handleResendCampaign}
                  sx={{
                    backgroundColor: `${bgColors.black} !important`,
                    height: "40px",
                    color: "white !important",
                    width: "100% !important",
                    fontSize: "14px !important",
                    fontWeight: "Semi Bold !important",
                    borderRadius: "8px !important",
                  }}
                >
                  Resend Campaign
                </LoadingButton>
              )}
            </>
          )}
        </Grid>
      </DialogActions>
    </Dialog>
  );
};

export default ResendCampaignPopup;
