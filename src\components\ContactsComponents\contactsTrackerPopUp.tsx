import {
  <PERSON><PERSON>,
  DialogTitle,
  <PERSON>alogContent,
  <PERSON>,
  Typo<PERSON>,
  IconButton,
  Card,
  CardContent,
  Grid,
  Chip,
  Popover,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { fetchAllContactsImportTracker } from "../../redux/slices/Contacts/GetAllContactsImportTracker";
import CloseSvg from "../../assets/svgs/CloseSvg";
import CommonTable from "../common/CommonTable";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import { FileDownloadOutlined as FileDownloadOutlinedIcon } from "@mui/icons-material";
import { CONTACTS_APIS } from "../../services/contacts";
import { CAMPAIGN_API } from "../../Apis/Campaign/Campaign";

const trackerStatus: any = {
  0: "Initiated",
  1: "Started",
  2: "In Progress",
  3: "Completed",
  4: "Failed",
};

const ContactsTracterPop = ({ open, handleClose }: any) => {
  const dispatch = useAppDispatch();
  const userData = useAppSelector((state: any) => state?.adminLogin?.data);
  const contactsImportTrackerDataSlice = useAppSelector(
    (state: any) => state?.contactsImportTrackerData
  );
  const contactsImportTrackerData = contactsImportTrackerDataSlice?.data?.data;

  const [analyticsPopoverAnchorEl, setAnalyticsPopoverAnchorEl] =
    useState<HTMLElement | null>(null);
  const [selectedTrackerAnalytics, setSelectedTrackerAnalytics] =
    useState<any>(null);
  const [page, setPage] = useState(1);
  const [rowsPerPage] = useState(10);
  //states for allInfo of contact in mobile view
  const [detailsMap, setDetailsMap] = useState<{ [key: string]: boolean }>({});
  const [anchorElMap, setAnchorElMap] = useState<{
    [key: string]: HTMLElement | null;
  }>({});
  const [downloadingReport, setDownloadingReport] = useState<string | null>(
    null
  );

  const handleDownloadReport = (row: any) => {
    const link = document.createElement("a");
    link.href = row.invalidDataS3BucketKey;
    link.setAttribute("download", `${row.s3BucketKey}`); // Suggests filename
    document.body.appendChild(link);
    link.click();
    link.remove();
  };

  // function to open the popover in mobile view
  const handleInfoClick = (
    event: React.MouseEvent<HTMLElement>,
    contactId: string
  ) => {
    setAnchorElMap((prev) => ({
      ...prev,
      [contactId]: event.currentTarget,
    }));
    setDetailsMap((prev) => ({
      ...prev,
      [contactId]: true,
    }));
  };
  //function to close the popover in mobile view
  const handleInfoClose = (contactId: string) => {
    setAnchorElMap((prev) => ({
      ...prev,
      [contactId]: null,
    }));
    setDetailsMap((prev) => ({
      ...prev,
      [contactId]: false,
    }));
  };

  const handleViewReport = (row: any, event: any) => {
    setAnalyticsPopoverAnchorEl(event.currentTarget);
    setSelectedTrackerAnalytics(row);
  };
  const handleCloseAnalyticsPopover = () => {
    setAnalyticsPopoverAnchorEl(null);
    setSelectedTrackerAnalytics(null);
  };

  const columns = [
    {
      id: "fileName",
      label: "File Name",
    },
    {
      id: "status",
      label: "Status",
      format: (value: number) => (
        <Typography
          sx={{
            color:
              value === 3 ? "#16a34a" : value === 4 ? "#dc2626" : "#3b82f6",
            fontWeight: 500,
          }}
        >
          {trackerStatus[value]}
        </Typography>
      ),
    },
    // {
    //   id: "createUser",
    //   label: "Created By",
    //   format: (value: any) => value.name,
    // },
    {
      id: "createdAt",
      label: "Created At",
      format: (value: string) => new Date(value).toLocaleString(),
    },
    {
      id: "message",
      label: "Message",
      format: (value: string) => (
        <Typography
          sx={{
            color: value === "Failed" ? "#dc2626" : "#16a34a",
            fontWeight: 500,
            whiteSpace: "pre-wrap",
          }}
        >
          {value || "-"}
        </Typography>
      ),
    },
  ];
  const renderAction = (row: any) => {
    return (
      <Box sx={{ display: "flex", justifyContent: "flex-start" }}>
        <Tooltip title="View Stats" placement="bottom">
          <Box>
            <IconButton onClick={(event) => handleViewReport(row, event)}>
              <VisibilityOutlinedIcon />
            </IconButton>
          </Box>
        </Tooltip>
        <Tooltip title="Download" placement="bottom">
          <Box>
            <IconButton
              onClick={() => handleDownloadReport(row)}
              disabled={downloadingReport === row.id}
            >
              {downloadingReport === row.id ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                <FileDownloadOutlinedIcon />
              )}
            </IconButton>
          </Box>
        </Tooltip>
      </Box>
    );
  };

  useEffect(() => {
    if (open) {
      const data = {
        businessId: userData?.companyId,
        pageNumber: page,
        pageSize: rowsPerPage,
      };
      dispatch(fetchAllContactsImportTracker(data));
    }
  }, [open, page, rowsPerPage]);

  const handlePageChange = (
    event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
  };

  const renderTrackerInMobile = (row: any) => (
    <Card
      key={row.id}
      sx={{
        mb: 2,
        boxShadow: "0px 2px 6px rgba(0, 0, 0, 0.1)",
        borderRadius: "8px",
        backgroundColor: "#fff",
      }}
    >
      <CardContent>
        <Grid container spacing={1.5}>
          <Grid
            item
            xs={12}
            sx={{ display: "flex", justifyContent: "space-between" }}
          >
            <Typography variant="subtitle1" fontWeight="bold">
              {row.fileName}
            </Typography>
            <IconButton
              size="small"
              onClick={(e) => handleInfoClick(e, row.contactId)}
              sx={{
                color: "#666",
                "&:hover": { color: "#3b82f6" },
              }}
            >
              <InfoOutlinedIcon fontSize="small" />
            </IconButton>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="caption" color="text.secondary">
              Status
            </Typography>
            <Box sx={{ mt: 0.5 }}>
              <Chip
                label={trackerStatus[row.status]}
                size="small"
                sx={{
                  backgroundColor:
                    row.status === 3
                      ? "#dcfce7"
                      : row.status === 4
                      ? "#fee2e2"
                      : "#dbeafe",
                  color:
                    row.status === 3
                      ? "#16a34a"
                      : row.status === 4
                      ? "#dc2626"
                      : "#3b82f6",
                  fontWeight: "normal",
                  fontSize: "0.75rem",
                }}
              />
            </Box>
          </Grid>
          <Popover
            open={Boolean(detailsMap[row.contactId])}
            anchorEl={anchorElMap[row.contactId]}
            onClose={() => handleInfoClose(row.contactId)}
            anchorOrigin={{
              vertical: "center",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "center",
              horizontal: "left",
            }}
            PaperProps={{
              sx: {
                // width: "200px",
                maxWidth: "300px",
                p: 2,
                boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1)",
              },
            }}
          >
            <Grid
              item
              xs={12}
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                gap: 1,
              }}
            >
              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">
                  Total
                </Typography>
                <Typography variant="body2" sx={{ mt: 0.5 }}>
                  {row.totalCount}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">
                  Unique
                </Typography>
                <Typography variant="body2" sx={{ mt: 0.5 }}>
                  {row.distinctCount}
                </Typography>
              </Grid>
            </Grid>

            <Grid
              item
              xs={12}
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                gap: 1,
              }}
            >
              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">
                  Uploaded
                </Typography>
                <Typography variant="body2" sx={{ mt: 0.5 }}>
                  {row.totalUploadedCount}
                </Typography>
              </Grid>

              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">
                  Duplicate
                </Typography>
                <Typography variant="body2" sx={{ mt: 0.5 }}>
                  {row.duplicateCount}
                </Typography>
              </Grid>
            </Grid>

            <Grid item xs={6}>
              <Typography variant="caption" color="text.secondary">
                Invalid
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5 }}>
                {row.invalidCount}
              </Typography>
            </Grid>
          </Popover>

          <Grid item xs={6}>
            <Typography variant="caption" color="text.secondary">
              Created At
            </Typography>
            <Typography variant="body2" sx={{ mt: 0.5 }}>
              {new Date(row.createdAt).toLocaleString()}
            </Typography>
          </Grid>

          {row.message && (
            <Grid item xs={12}>
              <Typography variant="caption" color="text.secondary">
                Message
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: row.status === 4 ? "#dc2626" : "#16a34a",
                  mt: 0.5,
                  whiteSpace: "pre-wrap",
                }}
              >
                {row.message}
              </Typography>
            </Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  );

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="xl"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: "12px",
          minHeight: "80vh",
          maxWidth: "75vw",
        },
      }}
    >
      <DialogTitle sx={{ height: "80px" }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="h5" sx={{ fontWeight: "bold" }}>
            Import Tracker
          </Typography>
          <IconButton onClick={handleClose}>
            <CloseSvg />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ p: 0 }}>
        <CommonTable
          columns={columns}
          data={contactsImportTrackerData?.trackers || []}
          rowIdKey="id"
          count={contactsImportTrackerData?.totalCount}
          page={page}
          onPageChange={handlePageChange}
          totalPages={Math.ceil(
            (contactsImportTrackerData?.totalCount || 0) / rowsPerPage
          )}
          heightOfTable=" calc(80vh -  80px)"
          renderOnMobile={renderTrackerInMobile}
          perPage={rowsPerPage}
          actions={renderAction}
          isLoading={contactsImportTrackerDataSlice?.status === "loading"}
        />
      </DialogContent>
      <Popover
        open={Boolean(analyticsPopoverAnchorEl)}
        anchorEl={analyticsPopoverAnchorEl}
        onClose={handleCloseAnalyticsPopover}
        anchorOrigin={{
          vertical: "center", // Changed from 'bottom' to 'center'
          horizontal: "right", // Changed from 'left' to 'right'
        }}
        transformOrigin={{
          vertical: "center", // Changed from 'top' to 'center'
          horizontal: "left", // Keep as 'left'
        }}
        PaperProps={{
          sx: {
            boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.15)",
            borderRadius: "8px",
            maxHeight: "80vh", // Prevent popover from being too tall
            overflowY: "auto", // Add scroll if content is too long
          },
        }}
      >
        {selectedTrackerAnalytics && (
          <Box
            sx={{
              p: 2,
              pt: 3,
              minWidth: "auto",
              maxWidth: 400,
              "& > *:not(:last-child)": { mb: 1.5 },
            }}
          >
            <Typography variant="h6" sx={{ mb: 2 }}>
              Tracker Analytics
            </Typography>

            {/* Add your analytics data display here */}
            <Box
              sx={{ display: "flex", justifyContent: "space-between", gap: 1 }}
            >
              <Box sx={{ display: "flex", flexDirection: "column" }}>
                <Box>
                  <Typography variant="subtitle2" color="textSecondary">
                    Total Contacts
                  </Typography>
                  <Typography>
                    {selectedTrackerAnalytics.totalCount || 0}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="subtitle2" color="textSecondary">
                    Unique In Excel
                  </Typography>
                  <Typography>
                    {selectedTrackerAnalytics.distinctCount || 0}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="subtitle2" color="textSecondary">
                    Already Present
                  </Typography>
                  <Typography>
                    {selectedTrackerAnalytics.duplicateCount || 0}
                  </Typography>
                </Box>
              </Box>
              <Box sx={{ display: "flex", flexDirection: "column" }}>
                <Box>
                  <Typography variant="subtitle2" color="textSecondary">
                    Contacts Added
                  </Typography>
                  <Typography>
                    {selectedTrackerAnalytics.totalUploadedCount || 0}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="subtitle2" color="textSecondary">
                    Invalid Contacts
                  </Typography>
                  <Typography>
                    {selectedTrackerAnalytics.invalidCount || 0}
                  </Typography>
                </Box>
              </Box>
            </Box>
            <Box
              sx={{
                position: "absolute",
                right: 0,
                top: 0,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <IconButton onClick={handleCloseAnalyticsPopover}>
                <CloseSvg />
              </IconButton>
            </Box>

            {/* Add more analytics data fields as needed */}
          </Box>
        )}
      </Popover>
    </Dialog>
  );
};

export default ContactsTracterPop;
