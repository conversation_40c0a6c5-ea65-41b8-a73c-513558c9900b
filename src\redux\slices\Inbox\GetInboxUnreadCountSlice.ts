import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { INBOX_APIS } from "../../../Apis/Inbox/InboxApis";

export interface IData {
  status: "loading" | "succeeded" | "failed" | "idle";
  data: any;
}

const initialState: IData = {
  status: "idle",
  data: null,
};

export const getInboxUnreadCount = createAsyncThunk(
  "getInboxUnreadCount",
  async (data: any) => {
    const response = await INBOX_APIS.fetchUnreadCount(data);
    return response?.data;
  }
);

export const GetInboxUnreadCountSlice = createSlice({
  name: "GetInboxUnreadCountSlice",
  initialState,
  reducers: {
    setData: (state, action) => {
      state.data = action.payload;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(getInboxUnreadCount.pending, (state) => {
        state.status = "loading";
        //   state.error = ""
        state.data = [];
      })
      .addCase(getInboxUnreadCount.fulfilled, (state, action) => {
        state.status = "succeeded";
        //   state.error = ""
        state.data = action.payload;
      })
      .addCase(getInboxUnreadCount.rejected, (state) => {
        state.status = "failed";
        state.data = [];
        //   state.error = action.error.message || "";
      });
  },
});

export const getInboxUnreadCountActions = GetInboxUnreadCountSlice.actions;
export default GetInboxUnreadCountSlice.reducer;
