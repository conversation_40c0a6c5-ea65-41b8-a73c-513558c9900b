import { FolderSpecialOutlined, Send } from "@mui/icons-material";
import {
  Box,
  FormControl,
  IconButton,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { useEffect, useState } from "react";
import { Editor } from "react-draft-wysiwyg";
import { EditorState, Modifier } from "draft-js";
import { bgColors } from "../../utils/bgColors";

const useStyles: any = makeStyles(() => ({
  chatTextBox: (props: any) => ({
    position: "relative",
    border: `1px solid ${props.borderColor || "#262738"}`,
    borderRadius: 8,
  }),
  toolBarStyle: {
    border: "none",
    backgroundColor: "rgb(247, 244, 244)",
    padding: "10px 0",
    marginBottom: 0,
    order: 2,
    borderBottomLeftRadius: "10px",
    borderBottomRightRadius: "10px",
  },
  variable: {
    color: `${bgColors.green}`,
    fontWeight: "600 !important",
    fontSize: "14px !important",
    cursor: "pointer",
  },
  wrapperClassName: {
    position: "relative",
    display: "flex",
    flexDirection: "column",
    marginTop: "12px",
  },
  editorClassName: {
    padding: "10px",
    minHeight: "60px",
    maxHeight: "200px",
    fontSize: "14px",
    "& .DraftEditor-root": {
      "& .public-DraftStyleDefault-block": {
        margin: 0,
      },
      "& .public-DraftEditorPlaceholder-root": {
        margin: 0,
      },
      "& .public-DraftEditor-content": {
        cursor: "text",
      },
    },
  },
  fileUploadInput: {
    display: "none",
  },
  placeholder: {
    paddingTop: "22px",
    paddingLeft: "10px",
    fontSize: "14px",
    color: "#aaa",
    position: "absolute",
    pointerEvents: "none",
  },
  disable: {
    pointerEvents: "none",
    opacity: "0.5",
    cursor: "not-allowed",
  },
}));
export const DraftEditorComponent = ({
  predefinedVariables,
  editorState,
  setEditorState,
  handleEditorStateChange,
  handleSaveInboxSettings,
  reactDraftWysiwygToolbarOptionsarticle,
  blockRendererFn,
  file,
  handleFileUpload,
  chatAreaRef,
  borderColor,
  isSendButton,
  handleAddVariable,
  handleEditorAddPrebuildVariable,
  placeholder,
  setSelectedVariable,
  selectedVariable,
  bodyRef,
}: any) => {
  const classes = useStyles({ borderColor });
  const [, setSendIconPosition] = useState({
    top: "440px",
    right: "117px",
  });
  const [, setFileIconPosition] = useState({
    top: file ? "500px" : "400px",
    right: "815px",
  });

  const isEditorEmpty = (editorState: EditorState) => {
    const contentState = editorState?.getCurrentContent();
    return (
      !contentState?.hasText() &&
      contentState?.getBlockMap().first().getType() === "unstyled"
    );
  };

  const handlePastedText = (text: string, html?: string) => {
    const cleanedText = text.replace(/\u00A0/g, " "); // Don't remove multiple spaces here

    const contentState = editorState.getCurrentContent();
    const selection = editorState.getSelection();

    if (cleanedText.includes("\n")) {
      const lines = cleanedText.split("\n");

      // Remove selected content before inserting lines
      let newContentState = Modifier.removeRange(
        contentState,
        selection,
        "forward"
      );
      let workingSelection = newContentState.getSelectionAfter();

      lines.forEach((line, index) => {
        // ReplaceText works for empty or non-empty lines
        newContentState = Modifier.replaceText(
          newContentState,
          workingSelection,
          line
        );

        if (index < lines.length - 1) {
          newContentState = Modifier.splitBlock(
            newContentState,
            newContentState.getSelectionAfter()
          );
          workingSelection = newContentState.getSelectionAfter();
        }
      });

      const newEditorState = EditorState.push(
        editorState,
        newContentState,
        "insert-fragment"
      );

      handleEditorStateChange(newEditorState);
      return true;
    }

    // Fallback for single-line paste
    const newContentState = Modifier.replaceText(
      contentState,
      selection,
      cleanedText
    );

    const newEditorState = EditorState.push(
      editorState,
      newContentState,
      "insert-characters"
    );

    handleEditorStateChange(newEditorState);
    return true;
  };

  useEffect(() => {
    const handleScroll = () => {
      if (chatAreaRef?.current) {
        const scrollHeight = chatAreaRef?.current.scrollHeight;
        const scrollTop = chatAreaRef?.current.scrollTop;
        const clientHeight = chatAreaRef?.current.clientHeight;

        const scrolledToBottom = scrollHeight - scrollTop === clientHeight;

        if (scrolledToBottom) {
          setSendIconPosition({ top: "10px", right: "117px" });
          setFileIconPosition({
            top: file ? "530px" : "400px",
            right: "815px",
          });
        } else {
          setSendIconPosition({
            top: `${clientHeight - 10}px`,
            right: "117px",
          });
          setFileIconPosition({
            top: `${clientHeight - (file ? 130 : 0)}px`,
            right: "815px",
          });
        }
      }
    };

    const chatArea = chatAreaRef?.current;
    if (chatArea) {
      chatArea.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (chatArea) {
        chatArea.removeEventListener("scroll", handleScroll);
      }
    };
  }, [file]);

  return (
    <>
      <Box className={classes.chatTextBox}>
        {isEditorEmpty(editorState) && (
          <Typography className={classes.placeholder} sx={{ fontSize: 14 }}>
            {placeholder ? placeholder : "Enter body message"}{" "}
          </Typography>
        )}
        <Editor
          ref={bodyRef}
          editorState={editorState}
          handlePastedText={handlePastedText}
          customBlockRenderFunc={blockRendererFn}
          onEditorStateChange={handleEditorStateChange}
          toolbarClassName={classes.toolBarStyle}
          wrapperClassName={classes.wrapperClassName}
          editorClassName={classes.editorClassName}
          toolbar={reactDraftWysiwygToolbarOptionsarticle}
          toolbarCustomButtons={[
            <>
              {isSendButton ? (
                <>
                  <FileUpload handleFileUpload={handleFileUpload} />
                  <SendMessageIcon
                    handleSaveInboxSettings={handleSaveInboxSettings}
                  />
                </>
              ) : (
                <></>
              )}
            </>,
          ]}
        />
      </Box>
      <Box
        sx={{
          display: "flex",
          marginTop: "10px",
          gap: 2,
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {typeof handleAddVariable === "function" && (
          <Typography
            variant="h5"
            sx={{
              // width: 180,
              textAlign: "center",
            }}
            className={`${classes.variable} ${
              selectedVariable !== undefined &&
              selectedVariable !== "addVariable" &&
              selectedVariable !== ""
                ? classes.disable
                : ""
            }`}
            onClick={() => {
              handleAddVariable("body");
              if (setSelectedVariable) {
                setSelectedVariable("addVariable");
              }
            }}
          >
            + Variables
          </Typography>
        )}

        {predefinedVariables && (
          <FormControl
            sx={{
              minWidth: 200,
            }}
            size="small"
            className={`${classes.variable} ${
              selectedVariable !== "leadratVariable" && selectedVariable !== ""
                ? classes.disable
                : ""
            }`}
          >
            <Select
              labelId="preDefinedVariable"
              onChange={(event) => handleEditorAddPrebuildVariable(event)}
              value=""
              displayEmpty
              renderValue={() => "Leadrat Variable"}
              inputProps={{ style: { fontSize: 14 } }}
              sx={{
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#e0e0e0",
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#bdbdbd",
                },
                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#e0e0e0",
                },
              }}
            >
              <MenuItem value="" disabled>
                Leadrat Variable
              </MenuItem>
              {predefinedVariables?.map((variable: string) => (
                <MenuItem
                  key={variable}
                  value={variable}
                  sx={{
                    fontSize: "0.875rem",
                    "&:hover": {
                      backgroundColor: "rgba(76, 175, 80, 0.08)",
                    },
                    "&.Mui-selected": {
                      backgroundColor: "rgba(76, 175, 80, 0.12)",
                      "&:hover": {
                        backgroundColor: "rgba(76, 175, 80, 0.16)",
                      },
                    },
                  }}
                >
                  {variable}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}
      </Box>
    </>
  );
};

export const FileUpload = ({ handleFileUpload }: any) => {
  const classes = useStyles();

  return (
    <Box
      position="absolute"
      left="185px"
      sx={{ backgroundColor: "#fff", borderBottomLeftRadius: "8px" }}
    >
      <input
        type="file"
        accept=".mp4, .avi, .mov .mp3, .jpg, .jpeg, .png, .gif"
        className={classes.fileUploadInput}
        onChange={handleFileUpload}
        id="file-upload-input"
      />
      <label htmlFor="file-upload-input">
        <IconButton component="span">
          <FolderSpecialOutlined />
        </IconButton>
      </label>
    </Box>
  );
};

export const SendMessageIcon = ({ handleSaveInboxSettings }: any) => {
  return (
    <Box position="absolute" right="10px" sx={{ cursor: "pointer" }}>
      <IconButton
        sx={{
          background: "#0082B6 !important",
          border: "1px solid #0071A9",
          boxShadow: "0px 2px 2px rgba(0, 0, 0, 0.08)",
          borderRadius: "8px",
          width: "32px",
          height: "32px",
        }}
        color="primary"
        onClick={handleSaveInboxSettings}
      >
        <Send
          fontSize="small"
          sx={{
            color: "#D9D9D9",
            width: "16.65px",
            height: "16.06px",
          }}
        />
      </IconButton>
    </Box>
  );
};
