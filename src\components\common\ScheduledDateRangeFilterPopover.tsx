import React, { useState, useEffect } from "react";
import {
  Pop<PERSON>,
  Box,
  Typography,
  TextField,
  Button,
  IconButton,
  Divider,
  Chip,
  Paper,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { bgColors } from "../../utils/bgColors";
import CloseSvg from "../../assets/svgs/CloseSvg";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import EventIcon from "@mui/icons-material/Event";
import ClearIcon from "@mui/icons-material/Clear";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";

const useStyles = makeStyles({
  popoverContainer: {
    padding: "12px",
    minWidth: "280px",
    maxWidth: "320px",
  },
  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "12px",
  },
  closeButton: {
    color: bgColors.black1,
    padding: "2px",
    "&:hover": {
      backgroundColor: "rgba(0, 0, 0, 0.04)",
    },
  },
  title: {
    color: bgColors.black1,
    fontWeight: "600",
    fontSize: "13px",
  },
  dateFieldContainer: {
    width: "100%",
    height: "30px",
    marginBottom: "12px",
  },
  dateFieldContainerFrom: {
    width: "100%",
    height: "30px",
    marginBottom: "16px", // more gap after From Date
  },
  dateField: {
    "& .MuiInputBase-root": {
      height: "36px",
      borderRadius: "6px",
      border: `1px solid ${bgColors.gray3}`,
      backgroundColor: bgColors.white,
      "&:hover": {
        border: `1px solid ${bgColors.green}`,
        backgroundColor: "rgba(68, 71, 70, 0.02)",
      },
      "&.Mui-focused": {
        border: `2px solid ${bgColors.green}`,
        backgroundColor: bgColors.white,
        boxShadow: `0 0 0 2px rgba(68, 71, 70, 0.1)`,
      },
    },
    "& .MuiInputBase-input": {
      padding: "6px 10px",
      fontSize: "13px",
      color: bgColors.black1,
      "&::placeholder": {
        color: bgColors.gray1,
        opacity: 1,
      },
    },
    "& .MuiInputLabel-root": {
      fontSize: "11px",
      color: bgColors.gray1,
      "&.Mui-focused": {
        color: bgColors.green,
      },
    },
    "& .MuiOutlinedInput-notchedOutline": {
      border: "none",
    },
  },
  modernDatePicker: {
    "& .MuiInputBase-root": {
      height: "36px",
      borderRadius: "8px",
      border: `1px solid ${bgColors.gray3}`,
      backgroundColor: bgColors.white,
      transition: "all 0.2s ease",
      "&:hover": {
        border: `1px solid ${bgColors.green}`,
        backgroundColor: "rgba(68, 71, 70, 0.02)",
        transform: "translateY(-1px)",
        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
      },
      "&.Mui-focused": {
        border: `2px solid ${bgColors.green}`,
        backgroundColor: bgColors.white,
        boxShadow: `0 0 0 3px rgba(68, 71, 70, 0.1)`,
        transform: "translateY(-1px)",
      },
    },
    "& .MuiInputBase-input": {
      padding: "8px 12px",
      fontSize: "13px",
      color: bgColors.black1,
      fontWeight: "500",
      "&::placeholder": {
        color: bgColors.gray1,
        opacity: 0.7,
      },
    },
    "& .MuiInputLabel-root": {
      fontSize: "14px", // was 11px
      color: bgColors.black1, // more visible
      fontWeight: "600", // was 500
      marginBottom: "4px", // space below label
      background: bgColors.white, // makes label pop if field has border
      padding: "0 4px", // optional: pill effect
      "&.Mui-focused": {
        color: bgColors.green,
        fontWeight: "700",
      },
    },
    "& .MuiOutlinedInput-notchedOutline": {
      border: "none",
    },
    "& .MuiInputAdornment-root": {
      color: bgColors.green,
    },
  },
  clearIcon: {
    color: bgColors.gray1,
    fontSize: "16px",
    cursor: "pointer",
    transition: "all 0.2s ease",
    "&:hover": {
      color: bgColors.red,
      transform: "scale(1.1)",
    },
  },
  dateRangeDisplay: {
    display: "flex",
    alignItems: "center",
    gap: "6px",
    marginBottom: "10px",
    padding: "6px 10px",
    backgroundColor: bgColors.gray9,
    borderRadius: "6px",
    border: `1px solid ${bgColors.gray3}`,
  },
  dateChip: {
    backgroundColor: bgColors.green,
    color: bgColors.white,
    fontSize: "11px",
    height: "20px",
    "& .MuiChip-label": {
      padding: "0 6px",
    },
  },
  buttonContainer: {
    display: "flex",
    justifyContent: "space-between",
    gap: "8px",
    marginTop: "12px",
  },
  applyButton: {
    backgroundColor: bgColors.green,
    color: bgColors.white,
    borderRadius: "8px",
    height: "32px",
    fontSize: "12px",
    fontWeight: "600",
    textTransform: "none",
    transition: "all 0.2s ease",
    "&:hover": {
      transform: "translateY(-1px)",
      boxShadow: "0 4px 12px rgba(68, 71, 70, 0.3)",
    },
    "&:disabled": {
      backgroundColor: bgColors.gray3,
      opacity: 0.5,
      color: bgColors.gray1,
      transform: "none",
      boxShadow: "none",
    },
  },
  resetButton: {
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    height: "32px",
    fontSize: "12px",
    fontWeight: "600",
    textTransform: "none",
    backgroundColor: bgColors.white,
    transition: "all 0.2s ease",
    "&:hover": {
      backgroundColor: "rgba(68, 71, 70, 0.08)",
      transform: "translateY(-1px)",
      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
    },
  },
  quickDateContainer: {
    display: "flex",
    flexWrap: "wrap",
    gap: "6px",
    marginBottom: "12px",
  },
  quickDateChip: {
    border: `1px solid ${bgColors.gray3}`,
    borderRadius: "16px",
    fontSize: "11px",
    height: "26px",
    cursor: "pointer",
    backgroundColor: bgColors.white,
    fontWeight: "500",
    transition: "all 0.2s ease",
    "&:hover": {
      border: `1px solid ${bgColors.green}`,
      backgroundColor: "rgba(68, 71, 70, 0.04)",
      transform: "translateY(-1px)",
      boxShadow: "0 2px 6px rgba(0, 0, 0, 0.1)",
    },
    "&.selected": {
      backgroundColor: bgColors.green,
      color: bgColors.white,
      border: `1px solid ${bgColors.green}`,
      fontWeight: "600",
      transform: "translateY(-1px)",
      boxShadow: "0 2px 8px rgba(68, 71, 70, 0.3)",
    },
  },
  divider: {
    margin: "12px 0",
    borderColor: bgColors.gray3,
  },
  iconContainer: {
    display: "flex",
    alignItems: "center",
    gap: "6px",
    color: bgColors.gray1,
  },
  datePickerPopper: {
    "& .MuiPaper-root": {
      borderRadius: "12px",
      boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12)",
      border: `1px solid ${bgColors.gray3}`,
      overflow: "hidden",
    },
    "& .MuiPickersLayout-root": {
      backgroundColor: bgColors.white,
      padding: "8px",
    },
    "& .MuiDayCalendar-weekDayLabel": {
      color: bgColors.green,
      fontWeight: "600",
      fontSize: "11px",
      textTransform: "uppercase",
      letterSpacing: "0.5px",
    },
    "& .MuiPickersDay-root": {
      borderRadius: "8px",
      fontSize: "12px",
      fontWeight: "500",
      margin: "1px",
      "&:hover": {
        backgroundColor: "rgba(68, 71, 70, 0.08)",
        transform: "scale(1.05)",
        transition: "all 0.2s ease",
      },
      "&.Mui-selected": {
        backgroundColor: bgColors.green,
        color: bgColors.white,
        fontWeight: "600",
      },
      "&.MuiPickersDay-today": {
        border: `2px solid ${bgColors.green}`,
        color: bgColors.green,
        fontWeight: "600",
        backgroundColor: "rgba(68, 71, 70, 0.04)",
      },
      "&.MuiPickersDay-outsideMonth": {
        color: bgColors.gray1,
        opacity: 0.5,
      },
    },
    "& .MuiPickersCalendarHeader-root": {
      padding: "8px 0",
      "& .MuiPickersCalendarHeader-label": {
        fontSize: "13px",
        fontWeight: "600",
        color: bgColors.black1,
        textTransform: "capitalize",
      },
      "& .MuiIconButton-root": {
        color: bgColors.green,
        borderRadius: "6px",
        "&:hover": {
          backgroundColor: "rgba(68, 71, 70, 0.08)",
          transform: "scale(1.1)",
          transition: "all 0.2s ease",
        },
      },
    },
    "& .MuiDayCalendar-monthContainer": {
      padding: "4px",
    },
    "& .MuiPickersYear-yearButton": {
      borderRadius: "8px",
      fontSize: "12px",
      fontWeight: "500",
      margin: "1px",
      "&:hover": {
        backgroundColor: "rgba(68, 71, 70, 0.08)",
        transform: "scale(1.05)",
        transition: "all 0.2s ease",
      },
      "&.Mui-selected": {
        backgroundColor: bgColors.green,
        color: bgColors.white,
        fontWeight: "600",
      },
    },
  },
});

export interface DateRange {
  startDate: string;
  endDate: string;
}

export interface ScheduledDateRangeFilterPopoverProps {
  anchorEl: HTMLElement | null;
  handleClose: () => void;
  onApplyFilter: (dateRange: DateRange) => void;
  initialDateRange?: DateRange;
  title?: string;
}

// Future date options for scheduled campaigns
const quickDateOptions = [
  { label: "Today", value: "today" },
  { label: "Tomorrow", value: "tomorrow" },
  { label: "Next 7 days", value: "next7days" },
  { label: "Next 30 days", value: "next30days" },
  { label: "This month", value: "thisMonth" },
  { label: "Next month", value: "nextMonth" },
];

const ScheduledDateRangeFilterPopover: React.FC<ScheduledDateRangeFilterPopoverProps> = ({
  anchorEl,
  handleClose,
  onApplyFilter,
  initialDateRange,
  title = "Date Range Filter",
}) => {
  const classes = useStyles();
  const today = dayjs().format("YYYY-MM-DD");
  
  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: initialDateRange?.startDate || today, // Default to today
    endDate: initialDateRange?.endDate || "",
  });
  const [selectedQuickDate, setSelectedQuickDate] = useState<string>("");

  const getCurrentDate = () => {
    return dayjs().format("YYYY-MM-DD");
  };

  const formatDateForDisplay = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const handleStartDateChange = (date: Dayjs | null) => {
    // For scheduled campaigns, from date should always be today or later
    const newStartDate = date ? date.format("YYYY-MM-DD") : today;
    const todayDate = dayjs();
    const selectedDate = date || todayDate;
    
    // If selected date is before today, use today
    const finalStartDate = selectedDate.isBefore(todayDate, 'day') ? today : newStartDate;
    
    setDateRange((prev) => ({
      ...prev,
      startDate: finalStartDate,
    }));

    // Reset end date if start date is after current end date
    if (finalStartDate && dateRange.endDate && finalStartDate > dateRange.endDate) {
      setDateRange((prev) => ({
        ...prev,
        endDate: "",
      }));
    }

    // Check if the new date range matches any quick select option
    checkAndSetQuickSelect(finalStartDate, dateRange.endDate);
  };

  const handleEndDateChange = (date: Dayjs | null) => {
    const newEndDate = date ? date.format("YYYY-MM-DD") : "";
    setDateRange((prev) => ({
      ...prev,
      endDate: newEndDate,
    }));

    // Check if the new date range matches any quick select option
    checkAndSetQuickSelect(dateRange.startDate, newEndDate);
  };

  const checkAndSetQuickSelect = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) {
      setSelectedQuickDate("");
      return;
    }

    const today = dayjs();
    const currentDate = today.format("YYYY-MM-DD");

    // Check for today
    if (startDate === currentDate && endDate === currentDate) {
      setSelectedQuickDate("today");
      return;
    }

    // Check for tomorrow
    const tomorrow = today.add(1, "day").format("YYYY-MM-DD");
    if (startDate === currentDate && endDate === tomorrow) {
      setSelectedQuickDate("tomorrow");
      return;
    }

    // Check for next 7 days
    const next7Days = today.add(7, "day").format("YYYY-MM-DD");
    if (startDate === currentDate && endDate === next7Days) {
      setSelectedQuickDate("next7days");
      return;
    }

    // Check for next 30 days
    const next30Days = today.add(30, "day").format("YYYY-MM-DD");
    if (startDate === currentDate && endDate === next30Days) {
      setSelectedQuickDate("next30days");
      return;
    }

    // Check for this month (from today to end of month)
    const thisMonthEnd = today.endOf("month").format("YYYY-MM-DD");
    if (startDate === currentDate && endDate === thisMonthEnd) {
      setSelectedQuickDate("thisMonth");
      return;
    }

    // Check for next month
    const nextMonthStart = today.add(1, "month").startOf("month").format("YYYY-MM-DD");
    const nextMonthEnd = today.add(1, "month").endOf("month").format("YYYY-MM-DD");
    if (startDate === currentDate && endDate === nextMonthEnd) {
      setSelectedQuickDate("nextMonth");
      return;
    }

    // If no match found, clear quick select
    setSelectedQuickDate("");
  };

  const handleQuickDateSelect = (quickDateValue: string) => {
    const today = dayjs();
    let startDate = today.format("YYYY-MM-DD"); // Always start from today
    let endDate = "";

    switch (quickDateValue) {
      case "today":
        endDate = today.format("YYYY-MM-DD");
        break;
      case "tomorrow":
        endDate = today.add(1, "day").format("YYYY-MM-DD");
        break;
      case "next7days":
        endDate = today.add(7, "day").format("YYYY-MM-DD");
        break;
      case "next30days":
        endDate = today.add(30, "day").format("YYYY-MM-DD");
        break;
      case "thisMonth":
        endDate = today.endOf("month").format("YYYY-MM-DD");
        break;
      case "nextMonth":
        endDate = today.add(1, "month").endOf("month").format("YYYY-MM-DD");
        break;
    }

    setDateRange({ startDate, endDate });
    setSelectedQuickDate(quickDateValue);
  };

  const handleApplyFilter = () => {
    if (dateRange.startDate && dateRange.endDate) {
      onApplyFilter(dateRange);
    }
  };

  const handleReset = () => {
    setDateRange({ startDate: today, endDate: "" }); // Reset to today
    setSelectedQuickDate("");
  };

  const handleCloseWithReset = () => {
    setDateRange({ startDate: today, endDate: "" }); // Reset to today
    setSelectedQuickDate("");
    handleClose();
  };

  const isApplyDisabled = !dateRange.startDate || !dateRange.endDate;

  return (
    <Popover
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={handleCloseWithReset}
      anchorOrigin={{
        vertical: "top",
        horizontal: "right",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
      sx={{
        zIndex: 1300,
      }}
      PaperProps={{
        sx: {
          marginLeft: 1,
        },
      }}
    >
      <Paper elevation={3} className={classes.popoverContainer}>
        <Box className={classes.headerContainer}>
          <Box className={classes.iconContainer}>
            <EventIcon fontSize="small" />
            <Typography className={classes.title}>{title}</Typography>
          </Box>
          <IconButton
            onClick={handleCloseWithReset}
            className={classes.closeButton}
            size="small"
          >
            <CloseSvg />
          </IconButton>
        </Box>

        <Divider className={classes.divider} />

        {/* Quick Date Options */}
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ mb: 1, display: "block", fontSize: "11px" }}
        >
          Quick Select
        </Typography>
        <Box className={classes.quickDateContainer}>
          {quickDateOptions.map((option) => (
            <Chip
              key={option.value}
              label={option.label}
              className={`${classes.quickDateChip} ${
                selectedQuickDate === option.value ? "selected" : ""
              }`}
              onClick={() => handleQuickDateSelect(option.value)}
              size="small"
            />
          ))}
        </Box>

        <Divider className={classes.divider} />

        {/* Custom Date Range */}
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ mb: 1, display: "block", fontSize: "11px" }}
        >
          Custom Range
        </Typography>

        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <Box className={classes.dateFieldContainerFrom}>
            <DatePicker
              label="From Date"
              value={dayjs(dateRange.startDate)}
              onChange={handleStartDateChange}
              minDate={dayjs()} // Only allow today and future dates
              format="DD/MM/YYYY"
              slotProps={{
                textField: {
                  className: classes.modernDatePicker,
                  fullWidth: true,
                  size: "small",
                  InputLabelProps: {
                    shrink: true,
                  },
                },
                popper: {
                  className: classes.datePickerPopper,
                },
              }}
            />
          </Box>

          <Box className={classes.dateFieldContainer}>
            <DatePicker
              label="To Date"
              value={dateRange.endDate ? dayjs(dateRange.endDate) : null}
              onChange={handleEndDateChange}
              minDate={dayjs(dateRange.startDate)} // Must be after from date
              format="DD/MM/YYYY"
              disabled={!dateRange.startDate}
              slotProps={{
                textField: {
                  className: classes.modernDatePicker,
                  fullWidth: true,
                  size: "small",
                  InputLabelProps: {
                    shrink: true,
                  },
                },
                popper: {
                  className: classes.datePickerPopper,
                },
              }}
            />
          </Box>
        </LocalizationProvider>

        {/* Date Range Display */}
        {dateRange.startDate && dateRange.endDate && (
          <Box className={classes.dateRangeDisplay}>
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ fontSize: "10px" }}
            >
              Selected:
            </Typography>
            <Chip
              label={`${formatDateForDisplay(
                dateRange.startDate
              )} - ${formatDateForDisplay(dateRange.endDate)}`}
              className={classes.dateChip}
              size="small"
            />
            <IconButton
              onClick={handleReset}
              className={classes.clearIcon}
              size="small"
              sx={{ padding: "2px" }}
            >
              <ClearIcon fontSize="small" />
            </IconButton>
          </Box>
        )}

        <Box className={classes.buttonContainer}>
          <Button
            onClick={handleReset}
            className={classes.resetButton}
            variant="outlined"
            size="small"
          >
            Reset
          </Button>
          <Button
            onClick={handleApplyFilter}
            className={classes.applyButton}
            disabled={isApplyDisabled}
            variant="contained"
            size="small"
          >
            Apply Filter
          </Button>
        </Box>
      </Paper>
    </Popover>
  );
};

export default ScheduledDateRangeFilterPopover; 