import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { CAMPAIGN_API } from "../../../Apis/Campaign/Campaign";


export interface IData {
    status: "loading" | "succeeded" | "failed" | "idle";
    data: any;
  }

  const initialState: IData = {
    status: "idle",
    data: null,
  };

  export const campaignAllFilters= createAsyncThunk(
    "campaignAllFilters",
    async (data: any) => {
      const response = await CAMPAIGN_API.campaignAllFilters(data);
      return response?.data;
    }
  );


  export const campaignAllFiltersSlice = createSlice({
    name: "campaignAllFiltersSlice",
    initialState,
    reducers: {
      clearData: (state) => {
        state.status = "loading";
        state.data = null;
      },
    },
    extraReducers: (builder) => {
      builder
        .addCase(campaignAllFilters.pending, (state) => {
          state.status = "loading";
        })
        .addCase(campaignAllFilters.fulfilled, (state, action) => {
          state.status = "succeeded";
          state.data = action.payload;
        })
        .addCase(campaignAllFilters.rejected, (state) => {
          state.status = "failed";
        });
    },
  });

  export const campaignAllFiltersActions = campaignAllFiltersSlice.actions;
  export default campaignAllFiltersSlice.reducer;
