import React from "react";
import {
  Box,
  Button,
  IconButton,
  TextField,
  Toolt<PERSON>,
  Typography,
} from "@mui/material";
import {
  FilterList as FilterIcon,
  Search as SearchIcon,
  Add as AddIcon,
  Label,
} from "@mui/icons-material";
import GetIconComponent from "./GetIconComponent";
import { bgColors } from "../../utils/bgColors";

interface ToolbarAction {
  icon: React.ReactNode;
  label: string;
  onClick: (...args: any[]) => void;
  tooltip?: string;
  disabled?: boolean;
  show?: boolean;
}

interface ToolbarButton {
  label: string;
  onClick: (...args: any[]) => void;
  icon?: React.ReactNode;
  tooltip?: string;
  disabled?: boolean;
  show?: boolean;
}

export interface RenderToolbarProps {
  // Main filter (required)
  selectedMainFilter?: {
    id: string;
    value: string;
    icon?: React.ReactNode;
  };
  handleMainFilter?: (event: React.MouseEvent<HTMLElement>) => void;

  // Search (optional)
  searchProps?: {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
  };

  // Primary action button (optional)
  primaryAction?: ToolbarButton;

  // Additional toolbar actions (optional)
  toolbarActions?: ToolbarAction[];

  // Tags functionality (optional)
  tagsProps?: {
    onTagsClick: (event: React.MouseEvent<HTMLElement>) => void;
    show?: boolean;
  };
  onPageChange?: (event: React.ChangeEvent<unknown>, value: number) => void;
  optionalTypographyInRenderToolbar?: string;
}

const RenderToolbar: React.FC<RenderToolbarProps> = ({
  selectedMainFilter,
  handleMainFilter,
  searchProps,
  primaryAction,
  optionalTypographyInRenderToolbar,
  toolbarActions = [],
  tagsProps,
  onPageChange,
}) => {
  return (
    <Box
      sx={{
        p: 2,
        display: "flex",
        alignItems: "center",
        flexDirection: { xs: "column", md: "row" },
        justifyContent: "space-between",
        borderBottom: "1px solid #f0f0f0",
        gap: 2,
      }}
    >
      <Box
        sx={{ display: "flex", alignItems: "center", gap: 2, flexWrap: "wrap" }}
      >
        {/* This Typography was added for the Custom Auto Replies page */}
        {optionalTypographyInRenderToolbar && (
          <Typography
            variant="body2"
            sx={{ color: bgColors.gray1, fontSize: "16px" }}
          >
            {optionalTypographyInRenderToolbar}
          </Typography>
        )}
        {/* Main Filter Button - Only show when there's a value */}
        {selectedMainFilter && selectedMainFilter?.value && (
          <Button
            size="small"
            startIcon={
              selectedMainFilter.icon
                ? selectedMainFilter.icon
                : (
                  <GetIconComponent
                    column={selectedMainFilter?.id.split("-")[0] || "Status"}
                    option={
                      selectedMainFilter?.id.split("-")[1] || selectedMainFilter?.id
                    }
                  />
                )
            }
            onClick={handleMainFilter}
            sx={{
              textTransform: "none",
              color: "#666",
              backgroundColor: "#f5f5f5",
              "&:hover": {
                backgroundColor: "#e0e0e0",
              },
              minWidth: "auto",
            }}
          >
            {selectedMainFilter.value}
          </Button>
        )}

        {/* Tags Button - Optional */}
        {tagsProps && tagsProps.show !== false && (
          <Button
            variant="text"
            size="small"
            startIcon={<Label />}
            onClick={tagsProps.onTagsClick}
            sx={{
              color: "#666",
              textTransform: "none",
              backgroundColor: "#f5f5f5",
              "&:hover": {
                backgroundColor: "#e0e0e0",
              },
            }}
          >
            Tags
          </Button>
        )}

        {/* Toolbar Actions */}
        <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
          {toolbarActions.map(
            (action, index) =>
              action.show !== false && (
                <Tooltip key={index} title={action.tooltip || action.label}>
                  <IconButton
                    size="small"
                    onClick={action.onClick}
                    disabled={action.disabled}
                    sx={{
                      color: "#666",
                      backgroundColor: "#f5f5f5",
                      "&:hover": {
                        backgroundColor: "#e0e0e0",
                        color: "#3b82f6",
                      },
                    }}
                  >
                    {action.icon}
                  </IconButton>
                </Tooltip>
              )
          )}
        </Box>
      </Box>

      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 2,
          flexWrap: "wrap", // Allow wrapping
          width: { xs: "100%", md: "auto" },
        }}
      >
        {/* Search Field */}
        {searchProps && (
          <TextField
            variant="standard"
            size="small"
            placeholder={searchProps.placeholder || "Search..."}
            value={searchProps.value}
            onChange={(e) => {
              searchProps.onChange(e.target.value);
              onPageChange && onPageChange({} as any, 1);
            }}
            InputProps={{
              style: {
                padding: "10px",
                fontSize: "14px",
                height: "38px",
                fontWeight: "600 !important",
                border: `1px solid ${bgColors.gray3}`,
                display: "flex",
                alignItems: "center",
              },
              sx: {
                "& .MuiInputBase-input": {
                  padding: 0,
                },
              },
              disableUnderline: true,
              startAdornment: <SearchIcon sx={{ color: "#666", mr: 1 }} />,
            }}
            sx={{
              flexGrow: { xs: 1, md: 0 }, // Take full width on mobile
              width: { xs: "100%", md: 300 },
              "& .MuiOutlinedInput-root": {
                backgroundColor: "#fff",
                "&:hover fieldset": {
                  borderColor: "#3b82f6",
                },
                "&.Mui-focused fieldset": {
                  borderColor: "#3b82f6",
                },
              },
            }}
          />
        )}

        {/* Primary Action Button */}
        {primaryAction && primaryAction.show !== false && (
          <Tooltip title={primaryAction.tooltip || ""}>
            <Button
              variant="contained"
              size="small"
              startIcon={primaryAction.icon || <AddIcon />}
              onClick={primaryAction.onClick}
              disabled={primaryAction.disabled}
              sx={{
                textTransform: "none",
                backgroundColor: "#22c55e",
                "&:hover": { backgroundColor: "#16a34a" },
                width: { xs: "100%", md: "auto" },
                whiteSpace: "nowrap",
              }}
            >
              {primaryAction.label}
            </Button>
          </Tooltip>
        )}
      </Box>
    </Box>
  );
};

export default RenderToolbar;
