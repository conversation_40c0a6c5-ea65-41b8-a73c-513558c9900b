import { useState } from "react";
import TemplatePreviewLayout from "../../TemplateComponents/TemplateForm/templatePreviewLayout";
import { Box, Button, Paper, Typography } from "@mui/material";
// import { convertFromHTML } from "draft-js";
import {
  parseTextToHtml,
  refactorCarouselCardsForPreview,
  unescapeJsonString,
} from "../../../utils/functions";

import {
  CarouselContainer,
  CarouselContent,
  CarouselItem,
  CarouselTrack,
  NavigationButton,
  CarouselIndicators,
  CarouselIndicator,
} from "../../../utils/StyledComponents";
import PhoneIcon from "@mui/icons-material/Phone";
import LaunchIcon from "@mui/icons-material/Launch";
import ReplyAllIcon from "@mui/icons-material/ReplyAll";
import { BiLeftArrowAlt } from "react-icons/bi";
import { BiRightArrowAlt } from "react-icons/bi";

export const maxWidthStyle = {
  maxWidth: { xs: "260px", sm: "320px", md: "380px" }, // Reduced width for better mobile fit
};
export const maxHeightStyle = {
  maxHeight: { xs: "50px", md: "60px" },
  overflowY: "hidden",
};

const renderMessageContent = (
  msg: {
    templateBody: String | null;
    templateHeader: string | undefined;
    templateFooter: string | undefined;
    templateMediaType: any;
    templateMediaFile: string | File | undefined;
    mediaMimeType: string;
    mediaUrl: string | undefined;
    mediaFileName: any;
    textMessage: string;
    mediaCaption: any;
    carouselCards: any;
  },
  index: string | number,
  messageRefs: any,
  handleOpenMediaPopover: (arg0: any) => void,
  highlightText: (arg0: any) => any,
  linkifyText: (arg0: any) => any,
  buttonsArray:
    | {
        buttonType: string;
        buttonValue?: string | undefined;
        buttonName?: string | undefined;
      }[]
    | undefined
) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  let carouselCards = [];

  if (msg.carouselCards) {
    if (Array.isArray(msg.carouselCards)) {
      carouselCards = msg.carouselCards;
    } else if (typeof msg.carouselCards === "string") {
      try {
        carouselCards = refactorCarouselCardsForPreview(
          JSON.parse(msg.carouselCards)
        );
      } catch (error) {
        console.warn("Failed to parse carouselCards JSON:", error);
        carouselCards = [];
      }
    }
  }

  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const goToNext = () => {
    if (currentIndex < carouselCards.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  if (carouselCards.length) {
    return (
      <Box sx={maxWidthStyle}>
        <Box>
          {/* Add small bottom margin to reduce space */}
          <TemplatePreviewLayout
            header={""}
            body={unescapeJsonString(msg?.templateBody) || ""}
            footer={""}
            mediaType={msg?.templateMediaType}
            mediaFile={msg?.templateMediaFile}
            buttons={buttonsArray}
            carouselCards={carouselCards}
          />
        </Box>
        {msg.templateMediaType === 6 && (
          <CarouselContainer>
            <CarouselContent>
              <CarouselTrack
                sx={{
                  transform:
                    carouselCards.length === 1
                      ? "translateX(calc(50% - 120px))" // Center if only one card (half container width - half card width)
                      : `translateX(calc(50% - 120px - ${
                          currentIndex * 248
                        }px))`, // Center the current card
                  justifyContent: "flex-start",
                }}
              >
                {carouselCards.map((carousel: any, index: number) => {
                  return (
                    <CarouselItem
                      key={index}
                      sx={{
                        opacity: index === currentIndex ? 1 : 0.7, // Reduce opacity of non-active cards
                        zIndex: index === currentIndex ? 2 : 1, // Bring active card to front
                      }}
                    >
                      <Paper
                        key={index}
                        elevation={2}
                        sx={{
                          p: 0,
                          borderRadius: 2,
                          maxHeight: "320px", // Increased height
                          width: "100%",
                          display: "flex",
                          flexDirection: "column",
                          boxShadow:
                            index === currentIndex
                              ? "0 4px 12px rgba(24, 119, 242, 0.25)" // Highlight current card with blue shadow
                              : "0 2px 8px rgba(0,0,0,0.1)",
                          backgroundColor: "#fff",
                          transform:
                            index === currentIndex ? "scale(1.02)" : "scale(1)", // Slightly enlarge current card
                          transition:
                            "transform 300ms ease, box-shadow 300ms ease",
                        }}
                      >
                        {/* Media Section */}
                        <Box sx={{ position: "relative", width: "100%" }}>
                          {carousel.mediaUrlType === 4 ? (
                            <video
                              style={{
                                width: "100%",
                                height: "160px", // Increased height for media
                                objectFit: "cover",
                                borderTopLeftRadius: "8px",
                                borderTopRightRadius: "8px",
                              }}
                              src={
                                carousel.headerMediaUrl instanceof File
                                  ? URL.createObjectURL(carousel.headerMediaUrl)
                                  : carousel.headerMediaUrl
                              }
                              controls={false}
                            />
                          ) : (
                            <Box
                              component="img"
                              src={carousel.headerMediaUrl}
                              sx={{
                                width: "100%",
                                height: "160px", // Increased height for media
                                objectFit: "cover",
                                borderTopLeftRadius: "8px",
                                borderTopRightRadius: "8px",
                              }}
                            />
                          )}
                        </Box>

                        {/* Content Section */}
                        <Box
                          sx={{
                            p: 1.5,
                            flex: "1 1 auto",
                            overflowY: "auto",
                            "&::-webkit-scrollbar": {
                              width: "4px",
                            },
                            "&::-webkit-scrollbar-thumb": {
                              backgroundColor: "#BDC7D8",
                              borderRadius: "4px",
                            },
                          }}
                        >
                          <Typography
                            variant="subtitle1"
                            component="h2"
                            sx={{
                              fontWeight: "600",
                              fontSize: "13px",
                              color: "#1C1E21",
                              mb: 0.5,
                              minHeight: "36px",
                              maxHeight: "none", // Allow content to expand
                              overflow: "visible", // Show all content
                            }}
                          >
                            {carousel.body}
                          </Typography>
                        </Box>

                        {/* Buttons Section */}
                        <Box
                          sx={{
                            borderTop: "1px solid #E4E6EB",
                            width: "100%",
                            marginTop: "auto", // Push to bottom of flex container
                            flex: "0 0 auto", // Don't allow flex to shrink or grow this section
                          }}
                        >
                          {carousel.carouselButtons?.map(
                            (button: any, btnIndex: number) => (
                              <Button
                                key={btnIndex}
                                startIcon={
                                  button?.buttonType === "PHONE_NUMBER" ? (
                                    <PhoneIcon />
                                  ) : button?.buttonType === "URL" ? (
                                    <LaunchIcon />
                                  ) : button?.buttonType === "QUICK_REPLY" ? (
                                    <ReplyAllIcon />
                                  ) : undefined
                                }
                                sx={{
                                  fontSize: 13,
                                  width: "100%",
                                  justifyContent: "center",
                                  textTransform: "none",
                                  py: 1,
                                  borderRadius: 0,
                                  borderBottom:
                                    btnIndex <
                                    carousel.carouselButtons.length - 1
                                      ? "1px solid #E4E6EB"
                                      : "none",
                                  color: "#1877F2", // Facebook blue
                                  "&:hover": {
                                    backgroundColor: "rgba(24, 119, 242, 0.05)",
                                  },
                                }}
                              >
                                {button?.buttonType === "PHONE_NUMBER"
                                  ? button?.buttonName
                                  : button?.buttonType === "URL"
                                  ? button?.buttonName
                                  : button?.buttonType === "QUICK_REPLY"
                                  ? button?.buttonValue || button?.buttonName
                                  : button.buttonValue}
                              </Button>
                            )
                          )}
                        </Box>
                      </Paper>
                    </CarouselItem>
                  );
                })}
              </CarouselTrack>
            </CarouselContent>

            {/* Navigation Arrows - Only show when needed */}
            {currentIndex > 0 && (
              <NavigationButton
                onClick={goToPrevious}
                aria-label="Previous slide"
                sx={{ left: "4px" }}
              >
                <BiLeftArrowAlt />
              </NavigationButton>
            )}

            {currentIndex < carouselCards.length - 1 && (
              <NavigationButton
                onClick={goToNext}
                aria-label="Next slide"
                sx={{ right: "4px" }}
              >
                <BiRightArrowAlt />
              </NavigationButton>
            )}

            {/* Navigation Dots */}
            <CarouselIndicators>
              {carouselCards.map((_: any, index: number) => (
                <CarouselIndicator
                  key={index}
                  active={index === currentIndex}
                  onClick={() => setCurrentIndex(index)}
                />
              ))}
            </CarouselIndicators>
          </CarouselContainer>
        )}
      </Box>
    );
  }
  if (msg?.templateBody && msg?.templateBody !== null) {
    return (
      <Box sx={maxWidthStyle}>
        <TemplatePreviewLayout
          header={msg?.templateHeader || ""}
          body={unescapeJsonString(msg?.templateBody) || ""}
          footer={msg?.templateFooter || ""}
          mediaType={msg?.templateMediaType}
          mediaFile={msg?.templateMediaFile}
          buttons={buttonsArray}
        />
      </Box>
    );
  }
  // if (msg?.mediaMimeType && !msg?.mediaUrl) return <>{msg?.mediaFileName}</>;
  if (msg?.textMessage) {
    return (
      <Box sx={maxWidthStyle}>
        <span
          ref={(el) => (messageRefs.current[index] = el)}
          dangerouslySetInnerHTML={{
            __html: highlightText(
              linkifyText(parseTextToHtml(msg?.textMessage))
            ),
          }}
        />
      </Box>
    );
  }
  if (["image/jpeg", "image/png"]?.includes(msg?.mediaMimeType)) {
    return (
      <Box sx={maxWidthStyle}>
        <img
          src={msg?.mediaUrl}
          alt="media"
          style={{ maxWidth: 330, cursor: "pointer" }}
          onClick={() => handleOpenMediaPopover(msg)}
        />
        <div
          dangerouslySetInnerHTML={{
            __html: highlightText(
              linkifyText(parseTextToHtml(msg?.mediaCaption || ""))
            ),
          }}
        />
      </Box>
    );
  }
  if (
    ["audio/mpeg", "audio/ogg", "audio/aar", "audio/amr"]?.includes(
      msg?.mediaMimeType?.split(";")[0]
    )
  ) {
    return (
      <Box sx={maxWidthStyle}>
        <audio
          src={msg?.mediaUrl}
          controls // Added controls for user interaction
          style={{ maxWidth: 330, cursor: "pointer" }}
          // onClick={() => handleOpenMediaPopover(msg)} // Optional if you need to handle click
        />
      </Box>
    );
  }

  if (msg?.mediaMimeType === "application/pdf") {
    return (
      <Box sx={maxWidthStyle}>
        <a
          href={msg?.mediaUrl}
          // download={msg?.mediaCaption}
          target="_blank"
          style={{ fontSize: "12px" }}
          rel="noreferrer"
        >
          <img
            src="/images/pdfIcon.png"
            alt=""
            style={{ height: 60, width: 60, cursor: "pointer" }}
          />
          <div
            dangerouslySetInnerHTML={{
              __html: highlightText(parseTextToHtml(msg?.mediaFileName || "")),
            }}
          />
        </a>
        <div
          dangerouslySetInnerHTML={{
            __html: highlightText(
              linkifyText(parseTextToHtml(msg?.mediaCaption || ""))
            ),
          }}
        />
      </Box>
    );
  }
  if (msg?.mediaMimeType === "text/plain") {
    return (
      <Box sx={maxWidthStyle}>
        <a
          href={msg?.mediaUrl}
          // download={msg?.mediaCaption}
          target="_blank"
          style={{ fontSize: "12px", textDecoration: "none" }}
          rel="noreferrer"
        >
          <img
            src="/images/txtIcon.png"
            alt=""
            style={{ height: 60, width: 60, cursor: "pointer" }}
          />
          <div
            dangerouslySetInnerHTML={{
              __html: highlightText(parseTextToHtml(msg?.mediaFileName || "")),
            }}
          />
        </a>
        <div
          dangerouslySetInnerHTML={{
            __html: highlightText(
              linkifyText(parseTextToHtml(msg?.mediaCaption || ""))
            ),
          }}
        />
      </Box>
    );
  }
  if (
    msg?.mediaMimeType ===
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
  ) {
    return (
      <Box sx={maxWidthStyle}>
        <a
          href={msg?.mediaUrl}
          // download={msg?.mediaCaption}
          target="_blank"
          style={{ fontSize: "12px", textDecoration: "none" }}
          rel="noreferrer"
        >
          <img
            src="/images/docxIcon.png"
            alt=""
            style={{ height: 60, width: 60, cursor: "pointer" }}
          />
          <div
            dangerouslySetInnerHTML={{
              __html: highlightText(parseTextToHtml(msg?.mediaFileName || "")),
            }}
          />
        </a>
        <div
          dangerouslySetInnerHTML={{
            __html: highlightText(
              linkifyText(parseTextToHtml(msg?.mediaCaption || ""))
            ),
          }}
        />
      </Box>
    );
  }
  if (msg?.mediaMimeType === "video/mp4") {
    return (
      <Box sx={maxWidthStyle}>
        <video controls src={msg?.mediaUrl} style={{ maxWidth: 330 }} />
        <div
          dangerouslySetInnerHTML={{
            __html: highlightText(
              linkifyText(parseTextToHtml(msg?.mediaCaption || ""))
            ),
          }}
        />
      </Box>
    );
  }
  if (
    msg?.mediaMimeType ===
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  ) {
    return (
      <Box sx={maxWidthStyle}>
        <a
          href={msg?.mediaUrl}
          // download={msg?.mediaCaption}
          target="_blank"
          style={{ fontSize: "12px", textDecoration: "none" }}
          rel="noreferrer"
        >
          <img
            src="/images/xlsxIcon.png"
            alt=""
            style={{ height: 60, width: 60, cursor: "pointer" }}
          />
          <div
            dangerouslySetInnerHTML={{
              __html: highlightText(parseTextToHtml(msg?.mediaFileName || "")),
            }}
          />
        </a>
        <div
          dangerouslySetInnerHTML={{
            __html: highlightText(
              linkifyText(parseTextToHtml(msg?.mediaCaption || ""))
            ),
          }}
        />
      </Box>
    );
  }
  return null;
};

export default renderMessageContent;
