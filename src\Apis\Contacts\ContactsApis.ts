/* global process */

import axios from "axios";
import { getStoredTokens } from "../../utils/authUtils";

const getAuthHeader = () => {
  const tokens = getStoredTokens();
  return tokens?.token ? `Bearer ${tokens?.token}` : "";
};

const CONTACTS_API_URL = process.env.REACT_APP_BASE_URL;

const syncContactsBySource = (data: any) => {
  return axios({
    url: `${CONTACTS_API_URL}/Integration/process-failedlead-generation?businessId=${data?.businessId}&source=${data?.source}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const fetchAllContactsByBusinessId = (data: any) => {
  return axios({
    url: `${CONTACTS_API_URL}/ContactsDetails?BusinessId=${data?.businessId}&UserId=${data?.userId}&page=${data?.page}&per_page=${data?.per_page}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data?.data,
  });
};

const createContact = (data: any) => {
  return axios({
    url: `${CONTACTS_API_URL}/ContactAdd?BusinessId=${data?.businessId}&UserId=${data?.userId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data?.data,
  });
};

const fetchContactDetailsById = (data: any) => {
  return axios({
    url: `${CONTACTS_API_URL}/ContactDetails?ContactId=${data.contactId}&BusinessId=${data?.businessId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const deleteContacts = (data: any) => {
  return axios({
    url: `${CONTACTS_API_URL}/ContactsRemove?BusinessId=${data?.BusinessId}&UserId=${data?.userId}`,
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: {
      contactIds: data?.data,
    },
  });
};

const getDeletedContacts = (data: any) => {
  const { page, per_page, filters } = data;

  const url = `${CONTACTS_API_URL}/GetDeletedContact?pageNumber=${page}&pageSize=${per_page}`;

  return axios({
    url,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: filters,
  });
};

const getRestoredContacts = (data: any) => {
  return axios({
    url: `${CONTACTS_API_URL}/RestoreContacts?businessId=${data?.businessId}&UserId=${data?.userId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: {
      contactIds: data.contactIds,
    },
  });
};

const updateContact = (data: any) => {
  return axios({
    url: `${CONTACTS_API_URL}/ContactEdit?BusinessId=${data?.businessId}&UserId=${data?.userId}&ContactId=${data?.contactId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data?.data,
  });
};

const importContacts = (data: any) => {
  const formData = new FormData();
  formData.append("File", data?.data);
  formData.append("ModuleType", data?.module);
  return axios({
    url: `${CONTACTS_API_URL}/ContactsImport?BusinessId=${data?.businessId}&UserId=${data?.userId}`,
    method: "POST",
    headers: {
      "Content-Type": "multipart/form-data",
      Authorization: getAuthHeader(),
    },
    data: formData,
  });
};

const contactsExcelUpload = (data: any) => {
  const formData = new FormData();
  formData.append("File", data?.data);
  formData.append("ModuleType", data?.module);
  return axios({
    url: `${CONTACTS_API_URL}/Contacts/excel?businessId=${data?.businessId}`,
    method: "POST",
    headers: {
      "Content-Type": "multipart/form-data",
      Authorization: getAuthHeader(),
    },
    data: formData,
  });
};

const contactsExcelBatchUpload = (data: any) => {
  return axios({
    url: `${CONTACTS_API_URL}/Contacts/batch?businessId=${data?.businessId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data.data,
  });
};

const exportContacts = (data: any) => {
  return axios({
    url: `${CONTACTS_API_URL}/ContactsExport?BusinessId=${data?.businessId}&UserId=${data?.userId}`,
    method: "GET",
    headers: {
      Authorization: getAuthHeader(),
    },
    responseType: "blob",
  });
};

const exportContactsById = (data: any) => {
  return axios({
    url: `${CONTACTS_API_URL}/ContactsExportById?BusinessId=${data?.businessId}&UserId=${data?.userId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    responseType: "blob",
    data: {
      contactIds: data?.contactId,
    },
  });
};

const getAllContactsImportTracker = (data: any) => {
  return axios({
    url: `${CONTACTS_API_URL}/GetAllContactsImportTracker?businessId=${data?.businessId}&pageNumber=${data?.pageNumber}&pageSize=${data?.pageSize}`,
    method: "GET",
    headers: {
      Authorization: getAuthHeader(),
    },
  });
};

export const CONTACTS_APIS = {
  fetchAllContactsByBusinessId,
  createContact,
  fetchContactDetailsById,
  deleteContacts,
  updateContact,
  importContacts,
  exportContacts,
  exportContactsById,
  getDeletedContacts,
  getRestoredContacts,
  getAllContactsImportTracker,
  syncContactsBySource,
  contactsExcelUpload,
  contactsExcelBatchUpload,
};
