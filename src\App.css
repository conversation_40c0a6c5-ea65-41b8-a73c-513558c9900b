body {
  font-family: "Inter";
}

/* Hide the action bar in the date picker */
.css-1st8yxe-MuiPickersLayout-root .MuiPickersLayout-actionBar {
  display: none;
}

/* Customize the scrollbar in the digital clock section */
.MuiMultiSectionDigitalClockSection-root::-webkit-scrollbar-thumb {
  width: 0px;
}

/* width of the scrollbar */
.MuiMultiSectionDigitalClockSection-root::-webkit-scrollbar {
  width: 0px;
}

/* track of the scrollbar */
.MuiMultiSectionDigitalClockSection-root::-webkit-scrollbar-track {
  background: transparent;
}
.custom-iphone-x .marvel-device.iphone-x.black {
  position: absolute;
  right: 10px;
}
.react-flow__attribution {
  display: none !important;
}

/* MUI Date Picker: Make selected year button green */
.MuiPickersYear-yearButton.Mui-selected {
  background-color: #1db954 !important; /* Use your bgColors.green hex here */
  color: #fff !important;
  font-weight: 600 !important;
}
