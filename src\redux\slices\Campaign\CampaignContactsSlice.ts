import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { CAMPAIGN_API } from "../../../Apis/Campaign/Campaign";


export interface IData {
    status: "loading" | "succeeded" | "failed" | "idle";
    data: any;
  }

  const initialState: IData = {
    status: "idle",
    data: null,
  };

  export const campaignContacts= createAsyncThunk(
    "campaignContacts",
    async (data: any) => {
      const response = await CAMPAIGN_API.campaignContacts(data);
      return response?.data;
    }
  );


  export const campaignContactsSlice = createSlice({
    name: "campaignContactsSlice",
    initialState,
    reducers: {},
    extraReducers: (builder) => {
      builder
        .addCase(campaignContacts.pending, (state) => {
          state.status = "loading";
        })
        .addCase(campaignContacts.fulfilled, (state, action) => {
          state.status = "succeeded";
          state.data = action.payload;
        })
        .addCase(campaignContacts.rejected, (state) => {
          state.status = "failed";
        });
    },
  });

  export const campaignContactsActions = campaignContactsSlice.actions
  export default campaignContactsSlice.reducer
