import React from "react";
import {
  Popover,
  Box,
  Typography,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  IconButton,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { bgColors } from "../../utils/bgColors";
import CloseSvg from "../../assets/svgs/CloseSvg";
import FilterListIcon from "@mui/icons-material/FilterList";

const useStyles = makeStyles({
  popoverContainer: {
    padding: "12px",
    minWidth: "200px",
    maxWidth: "250px",
  },
  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "12px",
  },
  closeButton: {
    color: bgColors.black1,
    padding: "2px",
    "&:hover": {
      backgroundColor: "rgba(0, 0, 0, 0.04)",
    },
  },
  title: {
    color: bgColors.black1,
    fontWeight: "600",
    fontSize: "13px",
    display: "flex",
    alignItems: "center",
    gap: "6px",
  },
  filterIcon: {
    color: bgColors.green,
    fontSize: "16px",
  },
  listContainer: {
    maxHeight: "200px",
    overflow: "auto",
  },
  listItem: {
    padding: "0",
    margin: "0",
    borderRadius: "4px",
    marginBottom: "2px",
    "&:hover": {
      backgroundColor: "rgba(68, 71, 70, 0.04)",
    },
  },
  listItemButton: {
    padding: "6px 10px",
    borderRadius: "4px",
    "&:hover": {
      backgroundColor: "rgba(68, 71, 70, 0.08)",
    },
    "&.Mui-selected": {
      backgroundColor: "rgba(68, 71, 70, 0.12)",
      "&:hover": {
        backgroundColor: "rgba(68, 71, 70, 0.16)",
      },
    },
  },
  listItemText: {
    "& .MuiTypography-root": {
      fontSize: "12px",
      fontWeight: "500",
      color: bgColors.black1,
    },
  },
  noOptions: {
    textAlign: "center",
    padding: "16px",
    color: bgColors.gray1,
    fontSize: "12px",
  },
});

export interface FilterOption {
  id: string;
  value: string;
  color?: string;
}

export interface ColumnFilterPopoverProps {
  anchorEl: HTMLElement | null;
  handleClose: () => void;
  columnId: string;
  columnLabel: string;
  filterOptions: FilterOption[];
  selectedFilter: string | null;
  onApplyFilter: (columnId: string, selectedValue: string) => void;
  onClearFilter: (columnId: string) => void;
}

const ColumnFilterPopover: React.FC<ColumnFilterPopoverProps> = ({
  anchorEl,
  handleClose,
  columnId,
  columnLabel,
  filterOptions,
  selectedFilter,
  onApplyFilter,
  onClearFilter,
}) => {
  const classes = useStyles();

  const handleFilterSelect = (filterValue: string) => {
    onApplyFilter(columnId, filterValue);
    // Don't close immediately - let parent component control when to close
    // handleClose();
  };

  return (
    <Popover
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
      sx={{
        zIndex: 1300,
      }}
    >
      <Box className={classes.popoverContainer}>
        <Box className={classes.headerContainer}>
          <Typography className={classes.title}>
            <FilterListIcon className={classes.filterIcon} />
            Filter {columnLabel}
          </Typography>
          <IconButton
            onClick={handleClose}
            className={classes.closeButton}
            size="small"
          >
            <CloseSvg />
          </IconButton>
        </Box>

        {filterOptions.length === 0 ? (
          <Box className={classes.noOptions}>
            <Typography>No filter options available</Typography>
          </Box>
        ) : (
          <Box className={classes.listContainer}>
            <List>
              {filterOptions.map((option) => (
                <ListItem
                  key={option.id}
                  className={classes.listItem}
                  disablePadding
                >
                  <ListItemButton
                    className={classes.listItemButton}
                    selected={selectedFilter === option.value}
                    onClick={() => handleFilterSelect(option.value)}
                  >
                    <ListItemText
                      primary={option.value}
                      className={classes.listItemText}
                    />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </Box>
        )}
      </Box>
    </Popover>
  );
};

export default ColumnFilterPopover; 