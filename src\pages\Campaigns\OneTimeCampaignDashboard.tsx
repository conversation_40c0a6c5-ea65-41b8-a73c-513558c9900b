import React, { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Box, Button, Tooltip } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import CommonHeader from "../../components/common/CommonHeader";
import { makeStyles } from "@mui/styles";
import CampaignInformation from "./CampaignInformation";
import CampaignAnalytics from "./CampaignAnalytics";
import { getCampaignById } from "../../redux/slices/Campaign/GetCampaignByIdSlice";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { fetchTemplateById } from "../../redux/slices/Templates/TemplateById";
import { getCampaignAnalyticsDetails } from "../../redux/slices/Campaign/GetCampaignAnalyticsDetailsSlice";

const stateMap = {
  1: { label: "Completed", color: "success" },
  2: { label: "Incomplete", color: "error" },
  3: { label: "Scheduled", color: "warning" },
};

const useStyles = makeStyles({
  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    // borderBottom: "1px solid #f0f0f0",
    width: "100%",
    // paddingLeft: "24px",
    paddingRight: "24px",
    paddingTop: "8px",
    paddingBottom: "8px",
  },
});

const OneTimeCampaignDashboard = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [tab, setTab] = useState<"details" | "analytics">("details");
  const classes = useStyles();
  const { data: campaignData, status: campaignStatus } = useAppSelector(
    (state: any) => state.getCampaignById
  );
  const { data: campaignAnalyticsData, status: campaignAnalyticsStatus } =
    useAppSelector((state: any) => state.getCampaignAnalyticsDetailsData);
  const { data: templateData } = useAppSelector(
    (state: any) => state.templateByIdData
  );
  const userInfoSlice = useAppSelector((state: any) => state.adminLogin);
  const userInfo = userInfoSlice?.data;
  const [campaignDetails, setCampaignDetails] = useState<any>(null);
  const [templateDetails, setTemplateDetails] = useState<any>(null);
  const [campaignAnalyticsDetails, setCampaignAnalyticsDetails] =
    useState<any>(null);

  // Refresh function to re-fetch campaign data and analytics
  const refreshCampaignData = () => {
    console.log("refreshCampaignData");
  };

  useEffect(() => {
    dispatch(getCampaignById({ campaignId: id }));
    dispatch(getCampaignAnalyticsDetails({ campaignId: id }));
  }, [id]);

  useEffect(() => {
    if (campaignData) {
      dispatch(
        fetchTemplateById({
          businessId: userInfo?.companyId,
          userId: userInfo?.userId,
          templateId: campaignData?.data?.templateId,
        })
      );
      setCampaignDetails(campaignData?.data);
    }
  }, [campaignData]);

  useEffect(() => {
    if (templateData) {
      setTemplateDetails(templateData[0]);
    }
  }, [templateData]);

  useEffect(() => {
    if (campaignAnalyticsData) {
      setCampaignAnalyticsDetails(campaignAnalyticsData?.data);
    }
  }, [campaignAnalyticsData]);

  return (
    <Box
      sx={{
        background: "#fff",
        boxSizing: "border-box",
        minHeight: "100vh",
      }}
    >
      {/* Top Section - Sticky within module */}
      <Box
        sx={{
          position: "sticky",
          top: 0,
          width: "100%",
          zIndex: 1100,
          background: "#fff",
          boxShadow: "0 2px 8px 0 rgba(60,72,88,.06)",
          p: 1,
          borderBottom: "1px solid #f0f0f0",
          height: "64px",
          display: "flex",
          alignItems: "center",
        }}
      >
        <Box className={classes.headerContainer} sx={{ width: "100%" }}>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Tooltip title="Back" sx={{ mr: 1, cursor: "pointer" }}>
              <ArrowBackIcon onClick={() => navigate("/campaigns/one-time")} />
            </Tooltip>
            <CommonHeader
              title={campaignDetails?.campaignTitle}
              sx={{
                "& .MuiTypography-root": {
                  fontSize: { xs: 16, sm: 18, md: 20 },
                },
              }}
            />
            <Box
              sx={{
                ml: 2,
                display: "flex",
                alignItems: "center",
                height: 32,
              }}
            >
              <Box
                sx={{
                  background:
                    stateMap[campaignDetails?.state as keyof typeof stateMap]
                      ?.color === "success"
                      ? "#D1FADF"
                      : stateMap[
                          campaignDetails?.state as keyof typeof stateMap
                        ]?.color === "error"
                      ? "#FEF2F2"
                      : "#FFFBEB",
                  color:
                    stateMap[campaignDetails?.state as keyof typeof stateMap]
                      ?.color === "success"
                      ? "#039855"
                      : stateMap[
                          campaignDetails?.state as keyof typeof stateMap
                        ]?.color === "error"
                      ? "#DC2626"
                      : "#DC2626",
                  fontWeight: 600,
                  fontSize: { xs: 12, sm: 14, md: 16 },
                  borderRadius: 99,
                  px: 2,
                  py: 0.5,
                  display: "flex",
                  alignItems: "center",
                }}
              >
                {
                  stateMap[campaignDetails?.state as keyof typeof stateMap]
                    ?.label
                }
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
      {/* Tabs (sticky) */}
      <Box
        sx={{
          position: "sticky",
          top: "64px",
          zIndex: 1099,
          display: "flex",
          alignItems: "center",
          background: "#F3F6FB",
          borderRadius: 2,
          minHeight: 40,
          boxShadow: "0 1px 6px 0 rgba(60,72,88,.04)",
          mx: { xs: 1, md: 3 },
          my: 1,
          height: "40px",
        }}
      >
        <Button
          onClick={() => setTab("details")}
          sx={{
            flex: 1,
            background: tab === "details" ? "#fff" : "#F3F6FB",
            color: tab === "details" ? "#15192C" : "#6B7280",
            fontWeight: 600,
            fontSize: { xs: 12, md: 16 },
            borderRadius: 2,
            border:
              tab === "details"
                ? "1.5px solid #E5E7EB"
                : "1.5px solid transparent",
            boxShadow:
              tab === "details" ? "0 2px 8px 0 rgba(60,72,88,.08)" : "none",
            py: 0.5,
            minHeight: 40,
            transition: "background 0.2s, border 0.2s, box-shadow 0.2s",
            textTransform: "none",
            zIndex: tab === "details" ? 1 : 0,
            height: "40px",
          }}
          disableElevation
        >
          Campaign Details
        </Button>
        <Button
          onClick={() => setTab("analytics")}
          sx={{
            flex: 1,
            background: tab === "analytics" ? "#fff" : "#F3F6FB",
            color: tab === "analytics" ? "#15192C" : "#6B7280",
            fontWeight: 600,
            fontSize: { xs: 12, md: 16 },
            borderRadius: 2,
            border:
              tab === "analytics"
                ? "1.5px solid #E5E7EB"
                : "1.5px solid transparent",
            boxShadow:
              tab === "analytics" ? "0 2px 8px 0 rgba(60,72,88,.08)" : "none",
            py: 0.5,
            minHeight: 40,
            transition: "background 0.2s, border 0.2s, box-shadow 0.2s",
            textTransform: "none",
            zIndex: tab === "analytics" ? 1 : 0,
            height: "40px",
          }}
          disableElevation
        >
          Campaign Analytics
        </Button>
      </Box>
      {/* Main Content - Scrollable */}
      <Box
        sx={{
          px: { xs: 1, md: 3 },
          height: "calc(100vh - 64px - 40px)",
          overflow: "auto",
        }}
      >
        {tab === "details" ? (
          <CampaignInformation
            campaignDetails={campaignDetails}
            templateDetails={templateDetails}
            campaignAnalyticsDetails={campaignAnalyticsDetails}
            isLoading={
              campaignStatus === "loading" ||
              campaignAnalyticsStatus === "loading"
            }
            refreshCampaignData={refreshCampaignData}
          />
        ) : (
          <CampaignAnalytics
            campaignAnalyticsDetails={campaignAnalyticsDetails}
            isLoading={
              campaignStatus === "loading" ||
              campaignAnalyticsStatus === "loading"
            }
          />
        )}
      </Box>
    </Box>
  );
};

export default OneTimeCampaignDashboard;
