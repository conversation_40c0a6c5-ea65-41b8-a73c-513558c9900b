import {
  Box,
  Checkbox,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  FormGroup,
  Typography,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import React, { useState } from "react";
import { bgColors } from "../../utils/bgColors";
import FileUpload from "./FileUpload";
import { CONTACTS_APIS } from "../../Apis/Contacts/ContactsApis";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { toastActions } from "../../utils/toastSlice";
import { fetchAllContacts } from "../../redux/slices/Contacts/AllContactsSlice";
import LoadingComponent from "../common/LoadingComponent";
import { CheckBox } from "@mui/icons-material";

const useStyles = makeStyles({
  signOutButtonStyles: {
    backgroundColor: "#3C3C3C",
    opacity: "60%",
    color: "#ffffff",
    height: "40px",
    borderRadius: "8px",
    // width: "190%",
    paddingInline: "27px ",
    padding: "8px",
    // fontWeight: "Bold",
    fontSize: "18px",
    // cursor: "pointer",
  },
  signOutButtonStylesContainer: {
    backgroundColor: "#000",
    opacity: "60%",
    color: "#ffffff",
    height: "40px",
    borderRadius: "8px",
    // width: "190%",
    paddingInline: "27px ",
    padding: "8px",
    // fontWeight: "Bold",
    fontSize: "18px",
    cursor: "pointer",
  },
  cancelButtonStyles: {
    backgroundColor: "#ffffff",
    color: "#000000",
    height: "40px",
    fontSize: "18px",
    borderRadius: "8px",
    border: `1px solid ${bgColors.gray3}`,
    // width: "180%",
    paddingInline: "27px ",
    padding: "8px",
    // fontWeight: "Bold",
    cursor: "pointer",
  },
  grayColor: {
    color: `${bgColors.black1} !important`,
    opacity: "60% !important",
    fontWeight: "600 !important",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    fontWeight: "600 !important",
  },
});

const ImportContactsPopUp: any = ({ open, handleClose }: any) => {
  const [selectedFiles, setSelectedFiles] = useState<any>([]);
  const [uploadedExcelData, setUploadedExcelData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedSheets, setSelectedSheets] = useState<any>([]);
  const userProfileSlice = useAppSelector((state: any) => state?.adminLogin);
  const contactsSlice: any = useAppSelector(
    (state: any) => state?.contactsData
  );

  const userData = userProfileSlice?.data;
  const dispatch = useAppDispatch();
  const classes = useStyles();
  const handleSheetSelection = (event: any) => {
    const { name, checked } = event.target;
    setSelectedSheets((prev: any) => {
      if (checked) {
        return [...prev, name];
      } else {
        return prev.filter((sheet: any) => sheet !== name);
      }
    });
  };

  const handleUpload = async () => {
    setIsLoading(true);
    try {
      // Check if a file is selected
      if (selectedFiles?.length === 0) {
        return;
      }
      const file = selectedFiles[0];

      // Check if the file is an Excel file
      const validMimeTypes = [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel",
      ];
      if (!validMimeTypes.includes(file?.type)) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: "File is not supported, only upload Excel files",
          })
        );
        return;
      }
      // Create a new FileReader object
      const reader = new FileReader();

      // Set up a callback function for when the file is fully loaded
      reader.onload = async (event) => {
        try {
          // Get the result of the file reading operation
          const binaryString = event.target?.result;

          // Check if the binary string is valid
          if (!binaryString) {
            return;
          }

          const bytes = new Uint8Array(binaryString as ArrayBuffer);

          const blob = new Blob([bytes], { type: selectedFiles[0].type });
          const file = new File([blob], "spreadsheet.xlsx", {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          });

          const data = {
            businessId: userData?.companyId,
            userId: userData?.userId,
            data: file,
            module: "contacts",
          };

          const getRes = await CONTACTS_APIS.contactsExcelUpload(data);
          if (getRes?.data?.success) {
            setUploadedExcelData(getRes?.data?.data);
            if (
              Object.keys(getRes?.data?.data?.multiSheetColumnNames).length ===
              1
            ) {
              setSelectedSheets(
                Object.keys(getRes?.data?.data?.multiSheetColumnNames)
              );
            }
          }
        } catch (error: any) {
          dispatch(
            toastActions.setToaster({
              type: "error",
              message: `${error?.response?.data?.message}`,
            })
          );
          setSelectedFiles([]);
          setUploadedExcelData(null);
          setSelectedSheets([]);
          setIsLoading(false);
          const inputField = document.getElementById("file-input");
          if (inputField) {
            (inputField as HTMLInputElement).value = "";
          }
        } finally {
          setIsLoading(false);
        }
      };

      // Read the selected file as a binary string
      reader.readAsArrayBuffer(selectedFiles[0]);
    } catch (error) {}
  };

  const handleImport = async () => {
    if (!selectedSheets || selectedSheets.length === 0) return;

    setIsLoading(true);

    try {
      const uploadPromises = selectedSheets.map((sheet: string) => {
        const payload = {
          businessId: userData?.companyId,
          data: {
            s3BucketKey: uploadedExcelData?.s3BucketKey,
            sheetName: sheet,
            fileName: uploadedExcelData?.fileName,
            uploadedFileId: uploadedExcelData?.uploadedFileId,
          },
        };
        return CONTACTS_APIS.contactsExcelBatchUpload(payload)
          .then((res) => ({
            status: "fulfilled",
            sheet,
            message: res?.data?.message,
          }))
          .catch(() => ({ status: "rejected", sheet }));
      });

      const results = await Promise.all(uploadPromises);

      const successMessages: string[] = [];
      const failedSheets: string[] = [];

      results.forEach((result) => {
        if (result.status === "fulfilled") {
          successMessages.push(`✔ ${result.sheet}`);
        } else {
          failedSheets.push(`❌ ${result.sheet}`);
        }
      });

      const finalMessage = [
        successMessages.length > 0 ? `${successMessages.join("\n")}` : null,
        failedSheets.length > 0 ? `${failedSheets.join("\n")}` : null,
      ]
        .filter(Boolean)
        .join("\n\n");

      dispatch(
        toastActions.setToaster({
          type: failedSheets.length > 0 ? "warning" : "success",
          message: finalMessage,
        })
      );

      handleClose();
    } catch (error: any) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: error?.message || "Something went wrong during import",
        })
      );
    } finally {
      setIsLoading(false);
      setUploadedExcelData(null);
      setSelectedSheets([]);
      setSelectedFiles([]);
      const inputField = document.getElementById("file-input");
      if (inputField) {
        (inputField as HTMLInputElement).value = "";
      }
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      PaperProps={{
        sx: {
          minWidth: { xs: "80%", sm: "25%" },
          borderRadius: "20px",
          padding: "10px",
        },
      }}
    >
      <Box
        mt={3}
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Typography
          textAlign="center"
          variant="h5"
          className={classes.blackColor}
        >
          Import Contacts
        </Typography>
      </Box>
      <DialogTitle>
        <Box></Box>
      </DialogTitle>
      <DialogContent>
        {uploadedExcelData ? (
          <Box>
            <Box
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Typography variant="body2" className={classes.grayColor}>
                Select the Sheets you want to Import,
              </Typography>
            </Box>
            <Box>
              <FormGroup>
                {Object.keys(uploadedExcelData.multiSheetColumnNames).map(
                  (sheet: string) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={selectedSheets.includes(sheet)}
                          onChange={handleSheetSelection}
                          name={sheet}
                        />
                      }
                      label={sheet}
                    />
                  )
                )}
              </FormGroup>
            </Box>
          </Box>
        ) : (
          <Box>
            <Box
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Typography variant="body2" className={classes.grayColor}>
                Select the CSV or xlsx format file with name(mandatory),
              </Typography>
              <Typography variant="body2" className={classes.grayColor}>
                {" "}
                country, contact number(mandatory), email id of file size{" "}
              </Typography>
              <Typography variant="body2" className={classes.grayColor}>
                {"< 10 MB to upload."}
              </Typography>
            </Box>
            {contactsSlice?.data?.exampleForImport && (
              <Box
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  marginTop: "10px",
                }}
              >
                <img
                  style={{ width: "30px", height: "30px" }}
                  src="/images/xlsxIcon.png"
                  alt="excel-file"
                />
                <a
                  className={classes.blackColor}
                  href={contactsSlice?.data?.exampleForImport}
                  download="excel_file.xlsx"
                >
                  Click here to download a sample file
                </a>
              </Box>
            )}
            <Box mt={3}>
              <FileUpload
                selectedFiles={selectedFiles}
                setSelectedFiles={setSelectedFiles}
              />
            </Box>
          </Box>
        )}
      </DialogContent>

      <Box
        mb={3}
        display="flex"
        flexDirection="row"
        justifyContent="center"
        gap={2}
        // alignSelf="center"
      >
        {!isLoading ? (
          <>
            <button
              className={classes.cancelButtonStyles}
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </button>
            {uploadedExcelData ? (
              <button
                className={
                  selectedSheets?.length
                    ? classes.signOutButtonStylesContainer
                    : classes.signOutButtonStyles
                }
                onClick={handleImport}
                disabled={isLoading || !selectedSheets?.length}
              >
                Import
              </button>
            ) : (
              <button
                className={
                  selectedFiles?.length
                    ? classes.signOutButtonStylesContainer
                    : classes.signOutButtonStyles
                }
                onClick={handleUpload}
                disabled={isLoading || !selectedFiles?.length}
              >
                Upload
              </button>
            )}
          </>
        ) : (
          <LoadingComponent height="auto" color={bgColors?.blue} />
        )}
      </Box>
    </Dialog>
  );
};

export default ImportContactsPopUp;
