import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Grid,
  Box,
  Button,
  IconButton,
  Tooltip,
} from "@mui/material";
import CommonTable from "../../components/common/CommonTable";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  <PERSON>Axis,
  Tooltip as Recharts<PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  LineChart,
  Line,
  CartesianGrid,
} from "recharts";
import { toastActions } from "../../utils/toastSlice";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { CAMPAIGN_API } from "../../Apis/Campaign/Campaign";
import { campaignContacts } from "../../redux/slices/Campaign/CampaignContactsSlice";
import { Chat } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { userInfo } from "os";

type StatBar = { name: string; value: number; color: string };

const lineData = [
  {
    name: "Day 1",
    Undelivered: 200,
    Delivered: 180,
    Failed: 10,
    Clicked: 40,
  },
  {
    name: "Day 2",
    Undelivered: 300,
    Delivered: 280,
    Failed: 5,
    Clicked: 60,
  },
  {
    name: "Day 3",
    Undelivered: 250,
    Delivered: 240,
    Failed: 8,
    Clicked: 55,
  },
  {
    name: "Day 4",
    Undelivered: 400,
    Delivered: 380,
    Failed: 12,
    Clicked: 90,
  },
];

const contactColumns = [
  { id: "name", label: "Name" },
  { id: "email", label: "Email" },
  { id: "phone", label: "Phone" },
];

// Define card labels for UI and data access
const cardLabels = [
  { label: "Undelivered", value: "Undelivered" },
  { label: "Delivered", value: "Delivered" },
  { label: "Read By", value: "readBy" },
  { label: "Failed", value: "Failed" },
  { label: "Replied", value: "Replied" },
] as const;

type CardValue = (typeof cardLabels)[number]["value"];

// Add isLoading prop
type CampaignAnalyticsProps = {
  campaignDetails: any;
  campaignAnalyticsDetails: any;
  isLoading?: boolean;
};

const CampaignAnalytics = ({
  campaignDetails,
  campaignAnalyticsDetails,
  isLoading,
}: CampaignAnalyticsProps) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const userInfoSlice = useAppSelector((state: any) => state.adminLogin);
  const userInfo = userInfoSlice?.data;
  const { data: campaignContactsData, status: campaignContactsStatus } =
    useAppSelector((state: any) => state.campaignContactsData);

  const [selectedCard, setSelectedCard] = useState<CardValue | null>(null);
  const [isContactsReportDownloading, setIsContactsReportDownloading] =
    useState(false);
  const [selectedContacts, setSelectedContacts] = useState<any>([]);

  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);

  // Create dynamic data based on campaignAnalyticsDetails
  const barData: StatBar[] = campaignAnalyticsDetails
    ? [
        {
          name: "Undelivered",
          value: campaignAnalyticsDetails.undelivered || 0,
          color: "#1976d2",
        },
        {
          name: "Delivered",
          value: campaignAnalyticsDetails.deliveredCount || 0,
          color: "#2e7d32",
        },
        {
          name: "Read By",
          value: campaignAnalyticsDetails.readCount || 0,
          color: "#f9a825", // yellow for 'Read By'
        },
        {
          name: "Failed",
          value: campaignAnalyticsDetails.failedCount || 0,
          color: "#d32f2f",
        },
        {
          name: "Replied",
          value: campaignAnalyticsDetails.repliedCount || 0,
          color: "#512da8",
        },
      ]
    : [];

  const pieData = campaignAnalyticsDetails
    ? [
        {
          name: "Undelivered",
          value: campaignAnalyticsDetails.undelivered || 0,
          color: "#1976d2",
        },
        {
          name: "Delivered",
          value: campaignAnalyticsDetails.deliveredCount || 0,
          color: "#2e7d32",
        },
        {
          name: "Read By",
          value: campaignAnalyticsDetails.readCount || 0,
          color: "#f9a825",
        },
        {
          name: "Failed",
          value: campaignAnalyticsDetails.failedCount || 0,
          color: "#d32f2f",
        },
        {
          name: "Replied",
          value: campaignAnalyticsDetails.repliedCount || 0,
          color: "#512da8",
        },
      ]
    : [];

  const handleCardClick = (value: CardValue) => {
    setSelectedCard(value);
    setPage(1);
  };

  const handleBackToCharts = () => {
    setSelectedCard(null);
    setPage(1);
  };

  // Use overviewCount as the single base for all stats
  const overviewCount = campaignAnalyticsDetails?.attemptedCount || 0;
  const getStatPercentage = (value: number) =>
    overviewCount ? ((value / overviewCount) * 100).toFixed(1) : "0.0";

  const deliveredCount = campaignAnalyticsDetails?.deliveredCount || 0;
  const failedCount = campaignAnalyticsDetails?.failedCount || 0;
  const readCount = campaignAnalyticsDetails?.readCount || 0;
  const repliedCount = campaignAnalyticsDetails?.repliedCount || 0;
  const undeliveredCount = campaignAnalyticsDetails?.undelivered || 0;

  const totalPages = Math.ceil(
    (selectedCard === null
      ? overviewCount
      : selectedCard === "Delivered"
      ? deliveredCount
      : selectedCard === "Failed"
      ? failedCount
      : selectedCard === "readBy"
      ? readCount
      : selectedCard === "Replied"
      ? repliedCount
      : selectedCard === "Undelivered"
      ? undeliveredCount
      : 0) / perPage
  );

  const cardBaseStyles = {
    height: 120,
    minWidth: 0,
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    transition: "box-shadow 0.2s, background 0.2s, border 0.2s",
  };
  const overviewColor = "#6C47FF"; // Unique color for Overview

  // Build sx for Overview card
  const overviewActive = selectedCard === null;
  const overviewSx = {
    ...cardBaseStyles,
    borderBottom: `4px solid ${overviewColor}`,
    cursor: "pointer",
    background: overviewActive ? overviewColor : "#fff",
    color: overviewActive ? "#fff" : "#15192C",
    ...(overviewActive && {
      boxShadow: `0 0 0 2px ${overviewColor}`,
      border: `2px solid ${overviewColor}`,
    }),
  };

  // Helper to get label from value
  const getLabelFromValue = (value: CardValue | null) => {
    if (!value) return "";
    const found = cardLabels.find((c) => c.value === value);
    return found ? found.label : value;
  };

  // Function to clean object by removing null, undefined, empty string, and empty array properties
  const cleanObject = (obj: any): any => {
    const cleaned: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (
        value !== null &&
        value !== undefined &&
        value !== "" &&
        !(Array.isArray(value) && value.length === 0)
      ) {
        cleaned[key] = value;
      }
    }
    return cleaned;
  };

  const handleDownloadContactReport = async (selectedCard: string) => {
    setIsContactsReportDownloading(true);
    try {
      const downloadData: any = {
        campaignId: campaignDetails?.campaignId,
        businessId: userInfo?.companyId,
        includeReplies: selectedCard === "Replied" ? true : false,
        isUndelivered: selectedCard === "Undelivered" ? true : false,
        status:
          selectedCard === "Delivered"
            ? 1
            : selectedCard === "Failed"
            ? 5
            : selectedCard === "Read"
            ? 2
            : null,
      };

      const response = await CAMPAIGN_API.getCampaignExcelReport(downloadData);

      const data = response?.data;

      if (data) {
        const blob = new Blob([data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${campaignDetails.campaignTitle}_${selectedCard}.xlsx`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        dispatch(
          toastActions.setToaster({
            type: "success",
            message: "Successfully downloaded the Excel file.",
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: "Unable to download the Excel file.",
          })
        );
      }
    } catch (error) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: "Error downloading the report.",
        })
      );
    } finally {
      setIsContactsReportDownloading(false);
    }
  };
  const fetchCampaignContacts = async () => {
    const data: any = {
      campaignId: campaignAnalyticsDetails?.campaignId,
      businessId: userInfo?.companyId,
      page: page,
      pageSize: perPage,
    };

    // Only add status property if selectedCard is not "Replied", "Undelivered", or null
    if (
      selectedCard !== "Replied" &&
      selectedCard !== "Undelivered" &&
      selectedCard !== null
    ) {
      data.status =
        selectedCard === "Delivered"
          ? 1
          : selectedCard === "Failed"
          ? 5
          : selectedCard === "readBy"
          ? 2
          : null;
    }

    data.IncludeReplies = selectedCard === "Replied" ? true : false;
    data.IsUndelivered = selectedCard === "Undelivered" ? true : false;

    // Clean the data object before dispatching
    const cleanedData = cleanObject(data);
    const response = await dispatch(campaignContacts(cleanedData));
    try {
      if (response?.meta?.requestStatus === "fulfilled") {
        setSelectedContacts(response?.payload?.data);
      } else {
        dispatch(
          toastActions.setToaster({
            message: response?.payload?.message || "Something went wrong",
            type: "error",
          })
        );
      }
    } catch (error: any) {
      dispatch(
        toastActions.setToaster({
          message: error?.message || "Something went wrong",
          type: "error",
        })
      );
    } finally {
    }
  };

  const renderCampaignContactsMobile = (row: any) => {
    return (
      <Card
        sx={{ m: 1, p: 2, display: "flex", flexDirection: "column", gap: 1 }}
      >
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 700 }}>
            {row.name}
          </Typography>
          {getLabelFromValue(selectedCard) === "Replied" && (
            <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}>
              <IconButton
                onClick={() => {
                  navigate(
                    `/inbox/${
                      row?.phone.startsWith("+")
                        ? row?.phone.slice(1)
                        : row?.phone
                    }`
                  );
                }}
              >
                <Chat />
              </IconButton>
            </Box>
          )}
        </Box>
        <Typography variant="body2">
          {" "}
          <span style={{ fontWeight: 500 }}>Email:</span> {row.email}
        </Typography>
        <Typography variant="body2">
          {" "}
          <span style={{ fontWeight: 500 }}>Phone:</span> {row.phone}
        </Typography>
        {getLabelFromValue(selectedCard) === "Failed" && (
          <Box sx={{ display: "flex", gap: 1 }}>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Error Message:
            </Typography>
            <Tooltip title={row?.errorMessage} placement="top" arrow>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                {row.errorMessage.length > 25
                  ? `${row?.errorMessage.slice(0, 25)}...`
                  : row?.errorMessage}
              </Typography>
            </Tooltip>
          </Box>
        )}
      </Card>
    );
  };

  useEffect(() => {
    fetchCampaignContacts();
  }, [campaignAnalyticsDetails?.campaignId, page, selectedCard, perPage]);

  // Map selectedContacts to table rows
  const mappedContacts = Array.isArray(selectedContacts)
    ? selectedContacts.map((contact: any) => ({
        name: contact.name || "-",
        email: contact.email || "-",
        phone: `${contact.countryCode || ""}${contact.contact || ""}`,
        errorMessage: contact.errorMessage || "-",
      }))
    : [];

  return (
    <Box>
      <Grid container spacing={2} sx={{ mt: 1, mb: 2 }}>
        {/* Overview Card */}
        <Grid item xs={12} md={2}>
          <Card sx={overviewSx} onClick={handleBackToCharts}>
            <Typography
              variant="h5"
              sx={{
                color: overviewActive ? "#fff" : overviewColor,
                fontWeight: 700,
              }}
            >
              {overviewCount.toLocaleString()}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: overviewActive ? "#fff" : "#6B7280",
                fontSize: 14,
                fontWeight: 700,
              }}
            >
              (100%)
            </Typography>
            <Typography
              variant="body2"
              sx={{ color: overviewActive ? "#fff" : overviewColor, mb: 1 }}
            >
              Overview
            </Typography>
          </Card>
        </Grid>
        {/* Stat Cards */}
        {cardLabels.map(({ label, value }) => {
          const stat = barData.find((s) => s.name === label);
          if (!stat) return null;
          const isActive = selectedCard === value;
          const statSx = {
            ...cardBaseStyles,
            borderBottom: `4px solid ${stat.color}`,
            cursor: "pointer",
            background: isActive ? stat.color : "#fff",
            color: isActive ? "#fff" : stat.color,
            ...(isActive && {
              boxShadow: `0 0 0 2px ${stat.color}`,
              border: `2px solid ${stat.color}`,
            }),
          };
          const showCount = stat.value;
          const showPercent = getStatPercentage(stat.value);
          return (
            <Grid item xs={6} md={2} key={value}>
              <Card sx={statSx} onClick={() => handleCardClick(value)}>
                <Typography
                  variant="h5"
                  sx={{
                    color: isActive ? "#fff" : stat.color,
                    fontWeight: 700,
                  }}
                >
                  {showCount.toLocaleString()}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: isActive ? "#fff" : "#6B7280",
                    fontSize: 14,
                    fontWeight: 700,
                  }}
                >
                  ({showPercent}%)
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: isActive ? "#fff" : "#6B7280" }}
                >
                  {label}
                </Typography>
              </Card>
            </Grid>
          );
        })}
      </Grid>
      {/* Main Content: Table or Charts */}
      {selectedCard ? (
        <Box sx={{ mb: 4 }}>
          <CommonTable
            columns={contactColumns}
            data={mappedContacts}
            rowIdKey="id"
            title={`${getLabelFromValue(selectedCard)} Contacts`}
            renderOnMobile={renderCampaignContactsMobile}
            page={page}
            perPage={perPage}
            perPageOptions={[10, 25, 50, 100, 200, 500]}
            onPerPageChange={(value) => {
              setPerPage(value);
              setPage(1);
            }}
            onPageChange={(_e, value) => setPage(value)}
            totalPages={totalPages}
            showPagination={true}
            selectable={false}
            isLoading={campaignContactsStatus === "loading"}
            heightOfTable="auto"
            noDataMessage="No data found"
            onRowClick={(row) => {}}
            {...(getLabelFromValue(selectedCard) === "Replied"
              ? {
                  actions: (row: any) => (
                    <Box
                      sx={{
                        display: "flex",
                        gap: 1,
                        justifyContent: "flex-start",
                      }}
                    >
                      <IconButton
                        onClick={() => {
                          navigate(
                            `/inbox/${
                              row?.phone.startsWith("+")
                                ? row?.phone.slice(1)
                                : row?.phone
                            }`
                          );
                        }}
                      >
                        <Chat />
                      </IconButton>
                    </Box>
                  ),
                  actionsLabel: "Actions",
                }
              : getLabelFromValue(selectedCard) === "Failed"
              ? {
                  actions: (row: any) => (
                    <Box
                      sx={{
                        display: "flex",
                        gap: 1,
                        justifyContent: "flex-start",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "200px",
                      }}
                    >
                      <Tooltip title={row.errorMessage} placement="top" arrow>
                        <Typography variant="body2" sx={{ color: "#6B7280" }}>
                          {row.errorMessage.length > 25
                            ? `${row.errorMessage.slice(0, 25)}...`
                            : row.errorMessage}
                        </Typography>
                      </Tooltip>
                    </Box>
                  ),
                  actionsLabel: "Error Message",
                }
              : {})}
            isContactsReportDownloading={isContactsReportDownloading}
            handleDownloadContactReport={handleDownloadContactReport}
          />
        </Box>
      ) : (
        <>
          {/* Charts Row: Bar Chart & Pie Chart */}
          <Grid container spacing={2} sx={{ mb: 4 }}>
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 2 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Campaign Performance
                </Typography>
                <ResponsiveContainer width="100%" height={220}>
                  <BarChart
                    data={barData}
                    margin={{ top: 10, right: 20, left: 0, bottom: 0 }}
                  >
                    <XAxis dataKey="name" />
                    <YAxis />
                    <RechartsTooltip />
                    <Bar dataKey="value" fill="#1976d2" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 2 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Audience Distribution
                </Typography>
                <ResponsiveContainer width="100%" height={220}>
                  <PieChart>
                    <Pie
                      data={pieData}
                      dataKey="value"
                      nameKey="name"
                      cx="50%"
                      cy="50%"
                      outerRadius={70}
                      label={({
                        cx = 0,
                        cy = 0,
                        midAngle = 0,
                        outerRadius = 0,
                        index = 0,
                        value = 0,
                      }) => {
                        const RADIAN = Math.PI / 180;
                        const radius = outerRadius + 24;
                        const x = cx + radius * Math.cos(-midAngle * RADIAN);
                        const y = cy + radius * Math.sin(-midAngle * RADIAN);
                        return (
                          <text
                            x={x}
                            y={y}
                            fill={pieData[index]?.color || "#000"}
                            textAnchor={x > cx ? "start" : "end"}
                            dominantBaseline="central"
                            fontSize={13}
                            fontWeight={600}
                            style={{ pointerEvents: "none" }}
                          >
                            {value > 0 ? value : ""}
                          </text>
                        );
                      }}
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Legend />
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Card>
            </Grid>
          </Grid>
          {/* Timeline Chart Row */}
          {/* <Card sx={{ p: 2, mb: 6 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Campaign Timeline
            </Typography>
            <ResponsiveContainer width="100%" height={220}>
              <LineChart
                data={lineData}
                margin={{ top: 10, right: 20, left: 0, bottom: 0 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <RechartsTooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="Undelivered"
                  stroke="#1976d2"
                  strokeWidth={2}
                />
                <Line
                  type="monotone"
                  dataKey="Delivered"
                  stroke="#2e7d32"
                  strokeWidth={2}
                />
                <Line
                  type="monotone"
                  dataKey="Failed"
                  stroke="#d32f2f"
                  strokeWidth={2}
                />
                <Line
                  type="monotone"
                  dataKey="Read By"
                  stroke="#f9a825"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </Card> */}
        </>
      )}
    </Box>
  );
};

export default CampaignAnalytics;
