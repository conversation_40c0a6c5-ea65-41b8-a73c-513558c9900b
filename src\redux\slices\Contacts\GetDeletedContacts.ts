import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { CONTACTS_APIS } from "../../../Apis/Contacts/ContactsApis";

export interface IData {
  status: "loading" | "succeeded" | "failed" | "idle";
  data: any;
}

const initialState: IData = {
  status: "idle",
  data: null,
};

export const fetchAllDeletedContacts = createAsyncThunk(
  "fetchAllDeletedContacts",
  async (data: any) => {
    const response = await CONTACTS_APIS.getDeletedContacts(data);
    return response?.data;
  }
);

export const allDeletedContactsSlice = createSlice({
  name: "allDeletedContactsSlice",
  initialState,
  reducers: {
    setData: (state, action) => {
      state.data = action.payload;
    },
    clearData: (state) => {
      state.status = "loading";
      state.data = null;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(fetchAllDeletedContacts.pending, (state) => {
        state.status = "loading";
        //   state.error = ""
        state.data = [];
      })
      .addCase(fetchAllDeletedContacts.fulfilled, (state, action) => {
        state.status = "succeeded";
        //   state.error = ""
        state.data = action.payload;
      })
      .addCase(fetchAllDeletedContacts.rejected, (state) => {
        state.status = "failed";
        state.data = [];
        //   state.error = action.error.message || "";
      });
  },
});

export const allDelectedContactsActions = allDeletedContactsSlice.actions;
export default allDeletedContactsSlice.reducer;
