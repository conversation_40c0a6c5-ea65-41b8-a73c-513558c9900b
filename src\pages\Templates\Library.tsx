import { bgColors } from "../../utils/bgColors";
import { makeStyles } from "@mui/styles";
import { Box, Card, Grid, Typography } from "@mui/material";
import React, { useCallback, useEffect, useRef, useState } from "react";
import TemplateCardView from "../../components/TemplateComponents/templateCard";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { fetchAllTemplatesByCompanyId } from "../../redux/slices/Templates/AllTemplatesSlice";
import LoadingComponent from "../../components/common/LoadingComponent";
import useDebouncedFetch from "../../utils/debounceHook";
import { checkTemplatesLibraryPermission } from "../../utils/permissions";
import NoAccessPage from "../../components/common/NoAccess";
import { useLocation, useNavigate } from "react-router-dom";
import CommonTable from "../../components/common/CommonTable";
import { categoryOptions, mainFilterOptions } from "../../utils/constants";

import CustomMainFilterPopover from "../../components/common/CustomMainFilerPopover";
import { DateRange } from "../../components/common/DateRangeFilterPopover";

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white1,

    height: "100vh",
    width: "100%",
    overFlow: "hidden !important",
  },
  bgContainer: {
    backgroundColor: bgColors.white,

    height: "100%",
    width: "100%",
    overFlow: "hidden !important",
    display: "flex",
    flexDirection: "column",
  },
  searchField: {
    width: "90%",

    // height: "38px",
    // backgroundColor: bgColors.white2,
    backgroundColor: "white",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "inter",
      color: "#000000 !important",
    },
  },
  manageContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    fontWeight: "600 !important",
  },
  grayColor: {
    color: `${bgColors.black1} !important`,
    opacity: "60% !important",
  },
  messageCountContainer: {
    // border: `2px solid ${bgColors.gray5}`,
    // borderRadius: "5px",
    // padding: "3px",
    // paddingRight: "5px",
  },
  messageInnerContainer: {
    border: "2px solid #F2F2F2",
    borderRadius: "6px",
    paddingInline: "4px",
    display: "flex",
    flexDirection: "row",
  },
  iconStyles: {
    cursor: "pointer",
    paddingLeft: "5px",
  },
  rotatedIcon: {
    cursor: "pointer",
    paddingRight: "5px",
    transform: "rotate(180deg)",
  },
  SaveChangesButton: {
    // backgroundColor: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    color: bgColors.green,
    borderRadius: "8px",
    width: "120px",
    height: "32px",
    // padding: "10px",
    cursor: "pointer",
  },
});

const TemplateLibrary = () => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  let templateRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const [noofCols, SetNoofCols] = useState("3");
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const tenantId = queryParams.get("tenantId");

  const userData = useAppSelector((state: any) => state?.adminLogin?.data);
  const templatesSlice = useAppSelector(
    (state: any) => state?.allTemplatesData
  );
  const removeTemplateSlice = useAppSelector(
    (state: any) => state?.removeTemplateData
  );
  const createTemplateSlice = useAppSelector(
    (state: any) => state?.createTemplateData
  );
  const updateTemplateSlice = useAppSelector(
    (state: any) => state?.updateTemplateData
  );
  // const activeTemplatesData = templatesSlice?.data?.data?.filter(
  //   (item: any) => item?.status !== null
  // );
  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const debouncedFetchTemplates = useDebouncedFetch(
    fetchAllTemplatesByCompanyId,
    1500
  );
  const templatesPermissionsArray = getuserPermissionData?.Templates;
  const hasTemplatesLibraryPermission = checkTemplatesLibraryPermission(
    templatesPermissionsArray
  );
  const libraryPermissionsObject = templatesPermissionsArray?.find(
    (item: any) => Object.prototype.hasOwnProperty.call(item, "library")
  );
  const libraryPermissionsActions = libraryPermissionsObject
    ? libraryPermissionsObject.library
    : [];

  const [
    newLibraryTemplatePermissionTooltipOpen,
    setNewLibraryTemplatePermissionTooltipOpen,
  ] = useState(false);
  const [
    editLibraryTemplatePermissionTooltipOpen,
    setEditLibraryTemplatePermissionTooltipOpen,
  ] = useState(false);
  const [selectedFilter, setSelectedFilter] = React.useState({
    id: "1",
    value: "View All",
  });
  const [dateRangeFilter, setDateRangeFilter] = React.useState<DateRange>({
    startDate: "",
    endDate: "",
  });
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [editObjectData, setEditObjectData] = React.useState({
    canEdit: false,
    templateId: "",
  });
  const mainFilterOptionsWithoutStatus = mainFilterOptions.filter(
    (option) => option.value !== "Status"
  );

  const [pageNumber, setPageNumber] = React.useState(1);
  const [search, setSearch] = React.useState<string>("");
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [pageData, setPageData] = useState(templatesSlice?.data?.data || []);
  const hasAccess = (permission: any) => {
    if (libraryPermissionsActions?.includes(permission)) {
      return true;
    }
    return false;
  };

  const handleTemplateFilter = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleCloseFilterPopover = () => {
    setAnchorEl(null);
  };
  const handleOptionClick = (option: any) => {
    if (option?.id === "1") {
      setSelectedFilter(option);
    }
    setPageNumber(1);
    handleCloseFilterPopover();
  };

  const handleDateRangeFilter = (dateRange: DateRange) => {
    setDateRangeFilter(dateRange);
    setPageNumber(1);
  };

  const handleEditToggle = (action: string, templateId: string) => {
    const hasPermission = hasAccess("editLibraryTemplate");
    if (hasPermission) {
      // setOpenDialog(true);
      setEditObjectData(
        action === "edit"
          ? { canEdit: true, templateId: templateId }
          : { canEdit: false, templateId: "" }
      );
      if (tenantId) {
        navigate(`/templates/${templateId}?tenantId=${tenantId}`, {
          state: { from: "/templates-library" },
        });
      } else {
        navigate(`/templates/${templateId}`, {
          state: { from: "/templates-library" },
        });
      }
    } else {
      setEditLibraryTemplatePermissionTooltipOpen(true);
      setTimeout(() => {
        setEditLibraryTemplatePermissionTooltipOpen(false);
      }, 2000);
    }
  };

  const tempWidth = (templateRef: any) => {
    if (!templateRef.current) return;
    const offsetWidth = templateRef.current.offsetWidth;
    const divWidth = [
      { width: 3200, cols: "10" },
      { width: 2500, cols: "9" },
      { width: 2250, cols: "8" },
      { width: 2000, cols: "7" },
      { width: 1800, cols: "6" },
      { width: 1400, cols: "5" },
      { width: 990, cols: "4" },
      { width: 900, cols: "3" },
      { width: 750, cols: "3" },
      { width: 650, cols: "2" },
      { width: 0, cols: "1" }, // catch-all case for widths below 650
    ];
    for (const { width, cols } of divWidth) {
      if (offsetWidth >= width) {
        SetNoofCols(cols);
        break;
      }
    }
  };

  useEffect(() => {
    const handleResize = () => tempWidth(templateRef);
    window.addEventListener("resize", handleResize);
    handleResize(); // initial check
    return () => window.removeEventListener("resize", handleResize);
  }, [templateRef]);

  useEffect(() => {
    // Build date filter conditions
    const dateConditions = [];
    if (dateRangeFilter.startDate && dateRangeFilter.endDate) {
      dateConditions.push(
        {
          column: "createdDate",
          operator: "gte",
          value: dateRangeFilter.startDate,
        },
        {
          column: "createdDate",
          operator: "lte",
          value: dateRangeFilter.endDate,
        }
      );
    }

    const postData = {
      userId: userData?.userId,
      businessId: userData?.companyId,
      pageNumber: pageNumber,
      per_page: 20,
      filters: {
        searching: {
          value: search,
        },
        sorting: {
          column: "",
          order: "",
        },
        filtering: {
          filterType: "and",
          conditions: [
            {
              column:
                selectedFilter?.id.split("-").length > 0
                  ? selectedFilter?.id.split("-")[0]
                  : "",
              operator: "equal",
              value:
                selectedFilter?.id.split("-").length > 0
                  ? selectedFilter?.id.split("-")[1]
                  : "",
            },
            ...dateConditions,
          ],
        },
      },
    };
    if (search) {
      debouncedFetchTemplates(postData);
    } else {
      // Clear search, call API immediately
      dispatch(fetchAllTemplatesByCompanyId(postData));
    }
    if (templateRef?.current) {
      tempWidth(templateRef);
    }
  }, [
    dispatch,
    search,
    selectedFilter,
    pageNumber,
    dateRangeFilter,
    createTemplateSlice,
    updateTemplateSlice,
    removeTemplateSlice,
  ]);

  const tableContainerRef = useRef(null);
  const handleScroll = () => {
    if (tableContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        tableContainerRef.current;
      if (
        scrollTop + clientHeight >= scrollHeight - 20 &&
        !isLoadingMore &&
        pageData.length !== templatesSlice?.data?.total
      ) {
        handleLoadMore();
      }
    }
  };

  const handleLoadMore = useCallback(() => {
    if (
      !isLoadingMore &&
      pageData.length !== templatesSlice?.data?.total &&
      pageNumber <= Math.ceil(templatesSlice?.data?.total / 40)
    ) {
      setIsLoadingMore(true);
      setPageNumber((prevPage) => prevPage + 1);
    }
  }, [isLoadingMore, pageData.length, templatesSlice?.data?.total]);

  const renderLibTemplateOnMobile = (row: any) => {
    return <Card key={row.templateId} sx={{ padding: 2, mb: 2 }}></Card>;
  };

  useEffect(() => {
    if (templatesSlice?.data?.data) {
      if (pageNumber === 1) {
        setPageData(templatesSlice?.data?.data);
      } else {
        // Ensure templatesSlice?.data?.data is an array before spreading
        const newTemplates = Array.isArray(templatesSlice?.data?.data)
          ? templatesSlice.data.data
          : [];

        setPageData((prevPageData: any) => [...prevPageData, ...newTemplates]);
      }

      setIsLoadingMore(false);
    }
  }, [templatesSlice?.data?.data]);

  return (
    <Grid className={classes.mainContainer} ref={templateRef}>
      {hasTemplatesLibraryPermission || tenantId ? (
        <Box className={classes.bgContainer}>
          <Box sx={{ height: { xs: "190px", sm: "160px", md: "120px" } }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: { xs: "column", md: "row" },
                justifyContent: "space-between",
              }}
            >
              <CommonTable
                title="Library"
                searchProps={{
                  value: search,
                  onChange: setSearch,
                  placeholder: "Search approved templates...",
                }}
                primaryAction={{
                  label: "Add Template",
                  onClick: () => handleEditToggle("add", ""),
                  disabled: !hasAccess("newLibraryTemplate"),
                  tooltip: !hasAccess("newLibraryTemplate")
                    ? "You don't have permission to add template"
                    : "",
                }}
                renderOnMobile={renderLibTemplateOnMobile}
                selectedMainFilter={selectedFilter}
                handleMainFilter={handleTemplateFilter}
                showPagination={false}
              />
            </Box>
          </Box>
          <CustomMainFilterPopover
            anchorEl={anchorEl}
            handleClose={handleCloseFilterPopover}
            mainFilterOptions={mainFilterOptionsWithoutStatus}
            subFilterOptions={categoryOptions}
            handleOptionClick={handleOptionClick}
            setSelectedFilter={setSelectedFilter}
          />
          <Box
            sx={{ flex: "1", overflow: "hidden", mt: { xs: 0, sm: 5, md: 0 } }}
          >
            {templatesSlice?.status === "loading" && !isLoadingMore ? (
              <LoadingComponent height="67vh" color={bgColors.blue} />
            ) : (
              <>
                <Box sx={{ overflow: "hidden", height: "90%" }}>
                  <Box
                    sx={{ overflow: "auto", height: "100%" }}
                    mt={1}
                    onScroll={handleScroll}
                    ref={tableContainerRef}
                  >
                    <Grid
                      container
                      gap={2}
                      p={{ xs: 2, md: 3 }}
                      sx={{
                        display:
                          pageData?.length !== 0 ? "inline-block" : "flex",
                        columns: `${noofCols}`,
                        columnGap: "auto",
                        width: "100%",
                        justifyContent: "center",
                      }}
                    >
                      {pageData?.length !== 0 ? (
                        pageData?.map((item: any, index: number) => (
                          <TemplateCardView
                            templateData={item}
                            userData={userData}
                            handleEditToggle={handleEditToggle}
                            key={index}
                            hasAccessCheck={hasAccess}
                            editLibraryTemplatePermissionTooltipOpen={
                              editLibraryTemplatePermissionTooltipOpen
                            }
                            setEditLibraryTemplatePermissionTooltipOpen={
                              setEditLibraryTemplatePermissionTooltipOpen
                            }
                          />
                        ))
                      ) : (
                        <Box
                          sx={{
                            height: "50vh",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          No Data Found
                        </Box>
                      )}
                    </Grid>
                  </Box>
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    paddingTop: "20px",
                  }}
                >
                  {pageData?.length !== 0 &&
                    pageData !== null &&
                    pageData.length !== templatesSlice?.data?.total &&
                    !isLoadingMore && (
                      <Typography
                        onClick={handleLoadMore}
                        sx={{
                          cursor: "pointer",
                          "&:hover": {
                            color: "blue",
                          },
                        }}
                      >
                        {" "}
                        {templatesSlice?.data?.total !== "loading" ? (
                          // <button
                          //   style={{
                          //     cursor: 'pointer',
                          //     fontSize: "14px",
                          //     borderRadius: '5px',
                          //     border: `1px solid ${bgColors.green}`,
                          //     backgroundColor: bgColors.white,
                          //     color: bgColors.green,
                          //     padding: '5px'
                          //   }}
                          // >
                          //   Load More...
                          // </button>
                          <></>
                        ) : (
                          ""
                        )}
                      </Typography>
                    )}
                  {isLoadingMore && (
                    <LoadingComponent height="20px" color={bgColors.blue} />
                  )}
                </Box>
              </>
            )}
          </Box>
        </Box>
      ) : (
        <NoAccessPage />
      )}
    </Grid>
  );
};

export default TemplateLibrary;
