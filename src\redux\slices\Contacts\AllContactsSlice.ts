import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { CONTACTS_APIS } from "../../../Apis/Contacts/ContactsApis";

export interface IData {
  status: "loading" | "succeeded" | "failed" | "idle";
  data: any;
}

const initialState: IData = {
  status: "idle",
  data: null,
};

export const fetchAllContacts = createAsyncThunk(
  "fetchAllContacts",
  async (data: any) => {
    const response = await CONTACTS_APIS.fetchAllContactsByBusinessId(data);
    return response?.data;
  }
);

export const allContactsSlice = createSlice({
  name: "allContactsSlice",
  initialState,
  reducers: {
    clearData: (state) => {
      state.status = "loading";
      state.data = null;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(fetchAllContacts.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchAllContacts.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload;
      })
      .addCase(fetchAllContacts.rejected, (state) => {
        state.status = "failed";
      });
  },
});

export const allContactsActions = allContactsSlice.actions;
export default allContactsSlice.reducer;
