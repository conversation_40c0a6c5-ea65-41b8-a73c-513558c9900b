import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import moment from "moment";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  InputLabel,
  Grid,
  FormControl,
  Select,
  MenuItem,
  InputAdornment,
  FormControlLabel,
  Switch,
  IconButton,
  Popover,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import CloseSvg from "../../assets/svgs/CloseSvg";
import { makeStyles } from "@mui/styles";
import TextFeildWithBorderComponet from "../common/TextFieldWithBorderComponent";
import InfoIcon from "@mui/icons-material/Info";
import ChatEmojiIcon from "../../assets/svgs/ChatEmojiIcon";
import ChatFileIcon from "../../assets/svgs/ChatFileIcon";
import ChatTemplate from "../../assets/svgs/ChatTemplate";
import { bgColors } from "../../utils/bgColors";
import { LocalizationProvider, TimePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import dayjs from "dayjs";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { createCampaign } from "../../redux/slices/Campaign/CreateCampaignSlice";
import EmojiPopover from "../InboxComponents/inboxDetailsComponents/EmojiPicker";
import CloseIconSvg from "../../assets/svgs/CloseIconSvg";
import { toastActions } from "../../utils/toastSlice";
import { LoadingButton } from "@mui/lab";
import LoadingComponent from "../common/LoadingComponent";
import TemplatePopUp from "../InboxComponents/inboxDetailsComponents/TemplatePopUp";
import { CAMPAIGN_API } from "../../Apis/Campaign/Campaign";
import { updateCampaign } from "../../redux/slices/Campaign/EditCampaignSlice";
import "moment-timezone";
import TemplatePreviewLayout from "../TemplateComponents/TemplateForm/templatePreviewLayout";
import { fetchTemplateById } from "../../redux/slices/Templates/TemplateById";
import CampaignIcon from "@mui/icons-material/Campaign";
import CloseIcon from "@mui/icons-material/Close";
import { getCampaignTitle } from "../../redux/slices/Campaign/GetCampaignTitleSlice";
import useDebouncedFetch from "../../utils/debounceHook";
import { getExpectedWalletDetails } from "../../redux/slices/Wallet/WalletSlice";
import { getCurrentDate } from "../../utils/functions";
import CommonAccordion from "../common/CommonAccordion";

import CustomReplyPopUpCampiagn from "./CustomReplyPopUpCampiagn";
import { validateForm } from "../../utils/validationUtils";
import { createAutoReplyMessage } from "../../redux/slices/Campaign/CreateAutoReplyMessageSlice";
import { debounce } from "lodash";
import { CampaignStatusEnum } from "../../pages/Campaigns/Campaigns";
import ContactSelectionOption from "./ContactSelectionOption";
import { getFileByBlobId } from "../../redux/slices/Campaign/GetFileByBlobId";
import { fetchAllContacts } from "../../redux/slices/Contacts/AllContactsSlice";

export enum ResponseType {
  CustomMessage = 1,
  Multiproduct = 2,
  Workflow = 3,
  ProductCollectionList = 4,
}
export enum CustomerResponse {
  None = 0,
  OnButtonClick = 1,
  TypedOutReply = 2,
  AnyCustomerResponse = 3,
}

const useStyles = makeStyles({
  popoverContainer: {
    padding: "16px",
  },
  greenButton: {
    color: "#1976d2",
    fontWeight: "700",
    borderRadius: 12,
    paddingInline: "6px",
  },
  yellowButton: {
    color: "#ff9800",
    fontWeight: "700",
    borderRadius: 12,
    paddingInline: "6px",
  },
  redButton: {
    // backgroundColor: bgColors.red2,
    color: bgColors.red1,
    fontWeight: "700",
    borderRadius: 12,
    paddingInline: "6px",
  },
  darkGreenButton: {
    // backgroundColor: bgColors.green,
    color: "#4caf50",
    fontWeight: "700",
    borderRadius: 12,
    paddingInline: "6px",
  },
  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "16px",
  },
  title: {
    color: bgColors.black1,
    fontWeight: "normal",
  },
  closeButton: {
    color: bgColors.black1,
  },
  selectContainer: {
    marginBottom: "16px",
  },
  textColor: {
    color: "#3C3C3C",
    fontWeight: "500",
  },
  changePasswordContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    // cursor: "pointer",
  },
  updateButtonStyles: {
    backgroundColor: "#3C3C3C",
    color: "#ffffff",
    height: "40px",
    borderRadius: "8px",
    width: "100%",
  },
  blackColor: {
    color: "#303030 !important",
    fontWeight: "500 !important",
    marginBottom: "8px",
  },
  button: {
    backgroundColor: `#3C3C3C !important`,
    width: "100% !important",
    fontSize: "24px !important",
    fontWeight: "Semi Bold !important",
    borderRadius: "8px !important",
  },
  messageCountContainer: {
    border: "1px solid #cdcdcd",
    borderRadius: "12px",
    padding: "8px",
    // width: "10px",
    paddingBottom: 6,
    justifyContent: "center",
    alignItems: "center",
  },
  messageInnerContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    // alignItems: "center",
  },
  messageCount: {
    backgroundColor: "#DBDBDB",
    borderRadius: "24px",
    padding: "3px",
    width: "40px",
    // color: "white",
    textAlign: "center",
  },
  grayColor: {
    color: "#4B5A5A !important",
    opacity: "60%",
    // padding:"5px"
  },
  iconStyles: {
    cursor: "pointer",
    // paddingLeft: "5px",
    // marginLeft:'385px'
  },
  cursor: {
    cursor: "pointer",
    fontSize: "10px",
  },
  buttonContainer: {
    display: "flex",
    justifyContent: "space-between",
  },
  resetBtnStyles: {
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    width: "100px",
    height: "38px",
    padding: "5px",
    cursor: "pointer",
    backgroundColor: "#fff",
    "&:hover": {
      backgroundColor: "rgba(68, 71, 70, 0.08)",
    },
  },
  SaveChangesButton: {
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    width: "130px",
    height: "38px",
    padding: "5px",
    cursor: "pointer",
    backgroundColor: "#fff",
    "&:hover": {
      backgroundColor: "rgba(68, 71, 70, 0.08)",
    },
  },
  SelctAlloption: {
    borderBottom: "1px solid rgba(0, 0, 0, 0.12) !important",
    // display: "flex !important",
    // flexDirection: "row",
    // justifyContent: "space-between !important",
    // alignItems: "center",
    // padding: "5px !important",
  },
  SelctAlloptionText: {
    display: "flex !important",
    flexDirection: "row",
    justifyContent: "space-between !important",
    alignItems: "center !important",
    width: "100% !important",
  },
  account: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    padding: "5px",
  },
  switch: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    fontSize: "16px !important",
    color: `#3C3C3C !important`,
  },
  textField: {
    height: "10px !important",
  },
  dateField: {
    width: "135px",
    // '& .MuiInputBase-input[type="date"]': {
    //   padding: "15px",
    // },
    // "& .MuiIconButton-root": {
    //   marginLeft: "-8px",
    // },
  },
  timefield: {
    ".css-jv54yp-MuiList-root-MuiMultiSectionDigitalClockSection-root": {
      scrollBehavior: "auto",
      scrollbarWidth: "none !important",
    },

    ".css-1st8yxe-MuiPickersLayout-root .MuiPickersLayout-actionBar": {
      display: "none",
    },
  },
});

interface CampaignState {
  name?: string;
  autoCustomAutomation: {
    input: string;
    responseType: ResponseType;
    customerResponse?: CustomerResponse;
    bodyMessage: string;
  } | null;
}

const EditCampaign = ({
  title,
  open,
  handleClose,
  data,
  searchCampaignQuery,
  selectedFilter1,
  fetchCampaignData,
}: any) => {
  const classes = useStyles();

  //contacts table states
  const [filterData, setFilterData] = useState<any>("");
  const [chatStatusFilter, setChatStatusFilter] = useState<any>("");
  const [contacts, setContacts] = useState([]);
  const [searchContactQuery, setSearchContactQuery] = useState<string>("");
  const [loadingContacts, setloadingContacts] = useState(false);
  const [contactsPage, setContactsPage] = useState(1);
  const [contactsPageCount, setContactsPageCount] = useState(1);
  const contactsPerPage = 9;

  // states for handling popup
  const [openDialog, setOpenDialog] = useState(false);
  const [emojiPopoverOpen, setEmojiPopoverOpen] = useState(false);
  const [isCustomReplyDialogOpen, setIsCustomReplyDialogOpen] = useState(false);
  const [outerDialogOpen, setOuterDialogOpen] = useState(true);
  const [expanded, setExpanded] = useState<string | false>(false);

  // other states to manage data
  const [anchorElement, setAnchorElement] = useState(null);
  const [selectedFile, setSelectedFile] = useState<any>(null);

  const [anchorEl, setAnchorEl] = useState(null);
  const [filters, setFilter] = useState<any>("");

  const [statusFilter, setStatusFilter] = useState<any>("");
  const [openAutocomplete, setOpenAutocomplete] = useState(false);
  const [sendFileToApi, setSendFileToApi] = useState<any>(null);

  const [isSwitchChecked, setSwitchChecked] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [templateData, setTemplateData] = useState<any>({});
  const [mediaType, setMediaType] = useState("");

  const [campaignState, setCampaignState] = useState<CampaignState>({
    name: "",
    autoCustomAutomation: {
      input: "",
      responseType: ResponseType.CustomMessage,
      customerResponse: CustomerResponse.OnButtonClick,
      bodyMessage: "",
    },
  });

  const [isEditLoading, setIsEditLoading] = useState(false);

  const [titleSearch, setTitleSearch] = useState("");

  const [formData, setFormData] = useState<any>({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    time: "",
    period: "",
    message: "",
    contacts: [],
    importedContactsFile: "",
    importedContactsFileId: "",
    importedContactsFileUrl: "",
    selectedDate: null,
    setSelectedTime: null,
  });

  const [formErrors, setFormErrors] = useState({
    name: "",
    description: "",
    message: "",
    contacts: "",
    importedContacts: "",
    chooseYourAudience: "",
  });
  const [formTouched, setFormTouched] = useState(false);
  const [existingCampaignName, setExistingCampaignName] = useState(true);

  const [choosedAudience, setChoosedAudience] = useState({
    showAutoComplete: false,
    showFileUpload: false,
  });
  const outerDialogRef = useRef<HTMLDivElement>(null);

  //Slice and redux functionality
  const dispatch = useAppDispatch();
  const debouncedFetchCampaigns = useDebouncedFetch(getCampaignTitle, 500);
  const userInfoSlice = useAppSelector((state: any) => state.adminLogin);
  const userInfo = userInfoSlice?.data;
  const createCamapignStatus = useAppSelector(
    (state: any) => state.addCampaign.status
  );
  const getCampaignByIdStatus = useAppSelector(
    (state: any) => state.getCampaignById.status
  );

  const tagsArray = useAppSelector(
    (state: any) => state.getContactTagsData.data
  );

  const searchResult = useAppSelector(
    (state: any) => state?.getCampaignTitle?.data?.success
  );

  const searchStatus = useAppSelector(
    (state: any) => state?.getCampaignTitle?.status
  );

  // handler functions

  const handleAccordionChange =
    (panel: string) => (_: React.ChangeEvent<{}>, isExpanded: boolean) => {
      setExpanded(isExpanded ? panel : false);
    };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      outerDialogRef.current &&
      !outerDialogRef.current.contains(event.target as Node)
    ) {
      setOuterDialogOpen(false);
    }
  };

  const handleCloseMainPopover = () => {
    setAnchorElement(null);
  };

  const handleCancel = () => {
    setSelectedFile(null);
    setSendFileToApi(null);
  };

  const setSendTemplatePayload = async (
    templateMessagePayload: any,
    templateState: any
  ) => {
    if (templateState && templateMessagePayload) {
      await setTemplateData(templateState);
      // setFormData((prev) => ({
      //   ...prev,
      //   message: templateState.body,
      // }));
      // setTemplateMessagePayload(templateMessagePayload);
    }
    // handleSendTemplateMessage(templateMessagePayload, templateState);

    setOpenDialog(false);
  };

  const templateDataById = async (params: any) => {
    const res = await dispatch(fetchTemplateById(params));

    const templateIdData = res?.payload;

    setTemplateData(templateIdData[0]);
  };

  const handleChangeFilters =
    () =>
    // filterName: string
    (event: any) => {
      setFilter(event.target.value);
    };

  const handleChangeStatusFilter =
    () =>
    // filterName: string
    (event: any) => {
      setStatusFilter(event.target.value);
    };

  const handleCloseAll = () => {
    setSelectedFile(null);
    setSwitchChecked(false);
    setCampaignState({
      name: "",
      autoCustomAutomation: {
        input: "",
        responseType: ResponseType.CustomMessage,
        customerResponse: CustomerResponse.OnButtonClick,
        bodyMessage: "Custom Body",
      },
    });
    setFormData({
      name: "",
      description: "",
      startDate: "",
      endDate: "",
      time: "",
      period: "",
      message: "",
      contacts: [],
      importedContactsFile: "",
      importedContactsFileId: "",
      importedContactsFileUrl: "",
      selectedDate: null,
      setSelectedTime: null,
    });
    setFormErrors({
      name: "",
      description: "",
      message: "",
      contacts: "",
      importedContacts: "",
      chooseYourAudience: "",
    });
    setSelectedFile(null);
    setTemplateData({});
    setExpanded(false);
    // setTemplateMessagePayload({});
    setFormTouched(false);
    // setValidCampaignMessage("");
    setChoosedAudience({
      showAutoComplete: false,
      showFileUpload: false,
    });
    setContacts([]);
    setloadingContacts(false);
    setFilterData("");
    setChatStatusFilter("");
    setSearchContactQuery("");
    setContactsPage(1);
    setContactsPageCount(1);
    handleClose();
  };

  const handleDateChange = (event: any) => {
    const newDate = event.target.value;
    setFormData({ ...formData, selectedDate: newDate });
  };

  const handleTimeChange = (newValue: any) => {
    setFormData({ ...formData, setSelectedTime: newValue });
  };

  const handleOpenDialog1 = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleOpenCustomReplyDialog = () => {
    setIsCustomReplyDialogOpen(true);
  };

  const handleCloseCampaignReplyPopup = () => {
    setIsCustomReplyDialogOpen(false);
  };

  const handleDeleteCustomReply = () => {
    setCampaignState((prev) => ({
      ...prev,
      autoCustomAutomation: null,
    }));
    dispatch(
      toastActions.setToaster({
        message: "Workflow deleted successfully.",
        type: "success",
      })
    );
  };

  const isValidForm = () => {
    const errors = { ...formErrors };
    let isValid = true;

    if (!formData?.name?.trim()) {
      errors.name = "Campaign name is required";
      isValid = false;
    }

    // else if (formData?.name?.trim().length > 30) {
    //   errors.name = "Campaign name cannot be more than 30 characters.";
    //   isValid = false;
    // }
    else if (searchResult === true && title !== "Edit") {
      errors.name = `${titleSearch} campaign name already exists.`;
      isValid = false;
    } else {
      errors.name = ""; // Clear any previous error
    }

    if (!formData?.message?.trim() && !templateData?.templateId) {
      errors.message = "Message is required.";
      isValid = false;
    } else {
      errors.message = ""; // Clear any previous error
    }

    if (
      !choosedAudience?.showAutoComplete &&
      !choosedAudience?.showFileUpload
    ) {
      errors.chooseYourAudience = "Audience is required";
      isValid = false;
    } else {
      errors.chooseYourAudience = ""; // Clear any previous error
    }
    if (!formData?.contacts?.length && choosedAudience?.showAutoComplete) {
      errors.contacts = "Contact is required.";
      isValid = false;
    } else {
      errors.contacts = ""; // Clear any previous error
    }
    if (!formData?.importedContactsFile && choosedAudience?.showFileUpload) {
      errors.importedContacts = "File is required";
      isValid = false;
    } else {
      errors.importedContacts = ""; // Clear any previous error
    }

    setFormErrors(errors);

    return isValid;
  };

  const handleSave = async () => {
    setIsEditLoading(true);
    if (isValidForm()) {
      if (isUploading) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: "File upload in progress. Please wait.",
          })
        );
        return;
      }
      // Check if scheduled date/time is in the past
      if (
        isSwitchChecked &&
        formData.selectedDate &&
        formData.setSelectedTime
      ) {
        const selectedDateTime = moment(
          `${formData.selectedDate}T${formData.setSelectedTime.format("HH:mm")}`
        );
        const currentDateTime = moment();

        if (selectedDateTime.isBefore(currentDateTime)) {
          dispatch(
            toastActions.setToaster({
              type: "error",
              message:
                "Campaign can only be scheduled after the current date and time.",
            })
          );
          setIsEditLoading(false);
          return;
        }
      }
      try {
        const getSelectedContactsIds = formData?.contacts?.map(
          (contact: any) => contact?.contactId
        );

        const dateTime = moment(
          `${formData?.selectedDate}T${
            typeof formData?.setSelectedTime !== "string" &&
            formData?.setSelectedTime?.format("HH:mm")
          }`
        )
          .utc()
          .format();

        if (title === "Add") {
          let payload: any = null;

          const hasAutoCustomAutomation =
            campaignState?.autoCustomAutomation &&
            campaignState.autoCustomAutomation.input !== "" &&
            campaignState.autoCustomAutomation.bodyMessage !== "";

          // Since only custom automation is available, simplify payload construction
          if (hasAutoCustomAutomation) {
            payload = {
              ...campaignState,
              name: formData?.name,
              workflowAutomation: null,
            };
          } else {
            payload = {
              ...campaignState,
              name: formData?.name,
              autoCustomAutomation: null,
              workflowAutomation: null,
            };
          }

          const data = {
            businessId: userInfo?.companyId || "",
            name: formData?.name?.trim() || "",
            audiences:
              getSelectedContactsIds.length > 0 ? getSelectedContactsIds : null,
            template: templateData?.templateId
              ? {
                  id: templateData?.templateId || null,
                  mediaType: templateData?.mediaType || null,
                  bodyValues:
                    templateData?.variables?.length > 0
                      ? templateData.variables
                          .filter((variable: any) => variable?.type === "body")
                          .map((variable: any) => ({
                            value: variable?.field || "",
                            fallbackValue: variable?.fallBackValue || "",
                          }))
                          .filter(
                            (item: any) =>
                              item.value !== "" || item.fallbackValue !== ""
                          ).length > 0
                        ? templateData.variables
                            .filter(
                              (variable: any) => variable?.type === "body"
                            )
                            .map((variable: any) => ({
                              value: variable?.field || "",
                              fallbackValue: variable?.fallBackValue || "",
                            }))
                        : null
                      : templateData?.leadratVariables?.length > 0
                      ? templateData.leadratVariables
                          .filter((variable: any) => variable?.type === "body")
                          .map((variable: any) => ({
                            value: variable?.field || "",
                            fallbackValue: variable?.fallBackValue || "",
                          }))
                          .filter(
                            (item: any) =>
                              item.value !== "" || item.fallbackValue !== ""
                          ).length > 0
                        ? templateData.leadratVariables
                            .filter(
                              (variable: any) => variable?.type === "body"
                            )
                            .map((variable: any) => ({
                              value: variable?.field || "",
                              fallbackValue: variable?.fallBackValue || "",
                            }))
                        : null
                      : null,
                  carouselVariables:
                    templateData?.carouselBodyVariables?.length > 0
                      ? templateData?.carouselBodyVariables
                          .map((cardVariables: any[]) => ({
                            bodyCarouselVariableValues: cardVariables
                              .filter(
                                (variable: any) => variable?.type === "carousel"
                              )
                              .map((variable: any) => ({
                                value: variable?.field,
                                fallbackValue: variable?.fallBackValue,
                              }))
                              .filter(
                                (item: any) =>
                                  item.value !== "" || item.fallbackValue !== ""
                              ),
                            redirectUrlVariableValues: [],
                            mediaUrl: "",
                          }))
                          .filter(
                            (card: any) =>
                              card.bodyCarouselVariableValues.length > 0
                          )
                      : templateData?.carouselLeadratBodyVariables?.length > 0
                      ? templateData?.carouselLeadratBodyVariables
                          .map((cardVariables: any[]) => ({
                            bodyCarouselVariableValues: cardVariables
                              .filter(
                                (variable: any) => variable?.type === "carousel"
                              )
                              .map((variable: any) => ({
                                value: variable?.field,
                                fallbackValue: variable?.fallBackValue,
                              }))
                              .filter(
                                (item: any) =>
                                  item.value !== "" || item.fallbackValue !== ""
                              ),
                            redirectUrlVariableValues: [],
                            mediaUrl: "",
                          }))
                          .filter(
                            (card: any) =>
                              card.bodyCarouselVariableValues.length > 0
                          )
                      : [],
                  headerValue:
                    templateData?.variables?.length > 0
                      ? templateData?.variables
                          ?.filter(
                            (variable: any) => variable?.type === "header"
                          )
                          .map((variable: any) => ({
                            value: variable?.field || "",
                            fallbackValue: variable?.fallBackValue || "",
                          }))[0] || null
                      : templateData?.leadratVariables?.length > 0
                      ? templateData?.leadratVariables
                          ?.filter(
                            (variable: any) => variable?.type === "header"
                          )
                          .map((variable: any) => ({
                            value: variable?.field || "",
                            fallbackValue: variable?.fallBackValue || "",
                          }))[0] || null
                      : null,
                }
              : null,
            scheduleDate: isSwitchChecked ? dateTime : null,
            text: formData?.message?.trim() || null,
            mediaUrl: sendFileToApi || null,
            uploadedFileId: formData?.importedContactsFileId
              ? formData?.importedContactsFileId
              : null,
          };

          const getCampaignRes = await dispatch(createCampaign(data));
          if (getCampaignRes?.payload?.success) {
            // Only call createAutoReplyMessage if there's actual custom automation data
            if (hasAutoCustomAutomation) {
              const response = await dispatch(createAutoReplyMessage(payload));

              // Check if createAutoReplyMessage failed
              if (response?.meta?.requestStatus === "rejected") {
                console.warn(
                  "Auto reply message creation failed:",
                  response?.payload
                );
                // Continue with campaign success flow even if auto reply fails
              }
            }
            setCampaignState({
              name: "",
              autoCustomAutomation: {
                input: "",
                responseType: ResponseType.CustomMessage,
                customerResponse: CustomerResponse.OnButtonClick,
                bodyMessage: "Custom Body",
              },
            });
            setTemplateData({});
            handleClose();
            setFormErrors({
              name: "",
              description: "",
              message: "",
              contacts: "",
              importedContacts: "",
              chooseYourAudience: "",
            });
            handleCloseAll();
            fetchCampaignData(1);

            dispatch(
              toastActions.setToaster({
                type: "success",
                message:
                  getCampaignRes?.payload?.message ===
                  "Campaign successfully sent."
                    ? getCampaignRes?.payload?.message
                    : "Campaign created successfully",
              })
            );
            dispatch(getExpectedWalletDetails(userInfo?.companyId));
          } else {
            dispatch(
              toastActions.setToaster({
                type: "error",
                message: getCampaignRes?.payload?.data?.message,
              })
            );
          }
        } else {
          const data1 = {
            businessId: userInfo?.companyId || "",
            name: formData?.name?.trim() || "",
            audiences:
              getSelectedContactsIds.length > 0 ? getSelectedContactsIds : null,
            template: templateData?.templateId
              ? {
                  id: templateData?.templateId || null,
                  mediaType: templateData?.mediaType || null,
                  bodyValues:
                    templateData?.variables?.length > 0
                      ? templateData.variables
                          .filter((variable: any) => variable?.type === "body")
                          .map((variable: any) => ({
                            value: variable?.field || "",
                            fallbackValue: variable?.fallBackValue || "",
                          }))
                          .filter(
                            (item: any) =>
                              item.value !== "" || item.fallbackValue !== ""
                          ).length > 0
                        ? templateData.variables
                            .filter(
                              (variable: any) => variable?.type === "body"
                            )
                            .map((variable: any) => ({
                              value: variable?.field || "",
                              fallbackValue: variable?.fallBackValue || "",
                            }))
                        : null
                      : templateData?.leadratVariables?.length > 0
                      ? templateData.leadratVariables
                          .filter((variable: any) => variable?.type === "body")
                          .map((variable: any) => ({
                            value: variable?.field || "",
                            fallbackValue: variable?.fallBackValue || "",
                          }))
                          .filter(
                            (item: any) =>
                              item.value !== "" || item.fallbackValue !== ""
                          ).length > 0
                        ? templateData.leadratVariables
                            .filter(
                              (variable: any) => variable?.type === "body"
                            )
                            .map((variable: any) => ({
                              value: variable?.field || "",
                              fallbackValue: variable?.fallBackValue || "",
                            }))
                        : null
                      : null,
                  headerValue:
                    templateData?.variables?.length > 0
                      ? templateData?.variables
                          ?.filter(
                            (variable: any) => variable?.type === "header"
                          )
                          .map((variable: any) => ({
                            value: variable?.field || "",
                            fallbackValue: variable?.fallBackValue || "",
                          }))[0] || null
                      : templateData?.leadratVariables?.length > 0
                      ? templateData?.leadratVariables
                          ?.filter(
                            (variable: any) => variable?.type === "header"
                          )
                          .map((variable: any) => ({
                            value: variable?.field || "",
                            fallbackValue: variable?.fallBackValue || "",
                          }))[0] || null
                      : null,
                }
              : null,
            scheduleDate: isSwitchChecked ? dateTime : null,
            text: templateData?.templateId || formData?.message?.trim() || null,
            mediaUrl: sendFileToApi || null,
            uploadedFileId: formData?.importedContactsFileId
              ? formData?.importedContactsFileId
              : null,
            id: data?.campaignId,
          };
          const getUpdateCampaignRes = await dispatch(createCampaign(data1));
          if (getUpdateCampaignRes?.meta?.requestStatus === "fulfilled") {
            let payload: any = null;

            const hasAutoCustomAutomation =
              campaignState?.autoCustomAutomation &&
              campaignState.autoCustomAutomation.input !== "" &&
              campaignState.autoCustomAutomation.bodyMessage !== "";

            // Since only custom automation is available, simplify payload construction
            if (hasAutoCustomAutomation) {
              payload = {
                ...campaignState,
                name: formData?.name,
                workflowAutomation: null,
              };
            } else {
              payload = {
                ...campaignState,
                name: formData?.name,
                autoCustomAutomation: null,
                workflowAutomation: null,
              };
            }

            // Only call createAutoReplyMessage if there's actual custom automation data
            if (hasAutoCustomAutomation) {
              const autoReplyResponse = await dispatch(
                createAutoReplyMessage(payload)
              );

              // Check if createAutoReplyMessage failed
              if (autoReplyResponse?.meta?.requestStatus === "rejected") {
                console.warn(
                  "Auto reply message creation failed:",
                  autoReplyResponse?.payload
                );
                // Continue with campaign update flow even if auto reply fails
              }
            }

            setFormErrors({
              name: "",
              description: "",
              message: "",
              contacts: "",
              importedContacts: "",
              chooseYourAudience: "",
            });

            setFormTouched(false);
            setSelectedFile(null);
            setSendFileToApi(null);
            setFormData({
              name: "",
              description: "",
              startDate: "",
              endDate: "",
              time: "",
              period: "",
              message: "",
              contacts: [],
              importedContactsFile: "",
              importedContactsFileId: "",
              importedContactsFileUrl: "",
              selectedDate: null,
              setSelectedTime: null,
            });
            setTemplateData({});

            fetchCampaignData(1);

            dispatch(getExpectedWalletDetails(userInfo?.companyId));
            dispatch(
              toastActions.setToaster({
                type: "success",
                message:
                  getUpdateCampaignRes?.payload?.message ||
                  "Campaign updated successfully",
              })
            );
            handleClose();
          } else {
            dispatch(
              toastActions.setToaster({
                type: "error",
                message: getUpdateCampaignRes?.payload?.data?.message,
              })
            );
          }
        }
      } catch (error: any) {
        console.error("Campaign creation error:", error);
        dispatch(
          toastActions.setToaster({
            type: "error",
            message:
              error?.message ||
              error?.payload?.message ||
              "An error occurred while processing the campaign",
          })
        );
      }
    }
    setIsEditLoading(false);
  };

  const handleSwitchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSwitchChecked(event.target.checked);
    if (!event.target.checked) {
      setFormData((prev: any) => ({
        ...prev,
        selectedDate: null,
        setSelectedTime: null,
      }));
    } else {
      setFormData((prev: any) => ({
        ...prev,
        selectedDate: getCurrentDate(),
        setSelectedTime: dayjs(),
      }));
    }
  };

  const handleApplyFilter = () => {
    setFilterData(filters);
    setChatStatusFilter(statusFilter);
    setOpenAutocomplete(true);
    handleCloseMainPopover();
  };

  const handleResetFilter = () => {
    setFilter("");
    setStatusFilter("");
    setFilterData("");
    setChatStatusFilter("");
    handleCloseMainPopover();
  };

  const handleTextChange = (event: any) => {
    const { name, value } = event.target;

    if (name === "name") {
      setTitleSearch(value);
    }

    setFormTouched(true);
    setFormData((prevData: any) => ({
      ...prevData,
      [name]: value,
    }));

    // isValidForm();
    setFormErrors((prevErrors) => ({
      ...prevErrors,
      [name]: "",
    }));
  };

  const handleCloseEmojiPopover = () => {
    setEmojiPopoverOpen(false);
  };

  const handleEmojiClick = (event: any) => {
    setAnchorEl(event.currentTarget);
    setEmojiPopoverOpen(true);
  };

  const messageRef: any = useRef(null);
  const handleEmojiSelect = (emoji: string) => {
    const input = messageRef.current.querySelector("textarea");

    if (input && typeof input.setSelectionRange === "function") {
      const start = input.selectionStart;
      const end = input.selectionEnd;

      const newMessage =
        formData.message.slice(0, start) + emoji + formData.message.slice(end);

      setFormData((prevData: any) => ({
        ...prevData,
        message: newMessage,
      }));

      // Set the cursor position after the newly added emoji
      setTimeout(() => {
        input.setSelectionRange(start + emoji.length, start + emoji.length);
        input.focus();
      }, 0);
    }
  };

  const handleFileIconClick = () => {
    fileInputRef.current?.click();
    // setIconColor("#4B5A5A");
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files && event.target.files[0];

    const maxImageSize = 5 * 1024 * 1024; // 5MB in bytes
    const maxDocSize = 100 * 1024 * 1024; // 100MB in bytes
    const maxVideoSize = 16 * 1024 * 1024; // 16MB in bytes

    if (file) {
      const { type, size } = file;
      const validImageTypes = ["image/jpeg", "image/jpg", "image/png"];
      const validDocTypes = ["application/pdf"];
      const validVideoTypes = ["video/mp4"];

      if (
        (validImageTypes.includes(type) && size <= maxImageSize) ||
        (validDocTypes.includes(type) && size <= maxDocSize) ||
        (validVideoTypes.includes(type) && size <= maxVideoSize)
      ) {
        setSelectedFile(file);
        // setIconColor("rgba(0, 0, 0, 0.35)");
      } else {
        if (validImageTypes.includes(type) && size > maxImageSize) {
          alert("Image files should be 5MB or smaller.");
        } else if (validDocTypes.includes(type) && size > maxDocSize) {
          alert("Document files should be 100MB or smaller.");
        } else if (validVideoTypes.includes(type) && size > maxVideoSize) {
          alert("Video files should be 16MB or smaller.");
        } else {
          alert(
            "Invalid file type. Please select a JPEG, JPG, PNG, PDF, or MP4 file."
          );
        }
        // setIconColor("rgba(0, 0, 0, 0.35)");
      }
    } else {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: "Please select a valid file.",
        })
      );
      // setIconColor("rgba(0, 0, 0, 0.35)");
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const memoizedMediaPlayer = useMemo(() => {
    if (selectedFile) {
      const isVideo =
        selectedFile.type?.startsWith("video/mp4") ||
        (typeof selectedFile === "string" &&
          (selectedFile.endsWith(".mp4") ||
            selectedFile.endsWith(".mov") ||
            selectedFile.endsWith(".mkv")));

      const isPDF =
        selectedFile.type === "application/pdf" ||
        (typeof selectedFile === "string" && selectedFile.endsWith(".pdf"));

      if (isVideo) {
        return (
          <Box sx={{ position: "relative", display: "inline-block" }}>
            <video
              controls
              src={
                typeof selectedFile === "string"
                  ? selectedFile
                  : URL.createObjectURL(selectedFile)
              }
              style={{ height: "100px", width: "200px" }}
            />
            <IconButton
              onClick={handleCancel}
              sx={{
                position: "absolute",
                top: 0,
                right: 0,
              }}
            >
              <CloseIconSvg />
            </IconButton>
          </Box>
        );
      }

      if (isPDF) {
        return (
          <Box sx={{ position: "relative", display: "inline-block" }}>
            <embed
              src={
                typeof selectedFile === "string"
                  ? selectedFile
                  : URL.createObjectURL(selectedFile)
              }
              type="application/pdf"
              style={{ height: "100px", width: "200px" }}
            />
            <IconButton
              onClick={handleCancel}
              sx={{
                position: "absolute",
                top: 0,
                right: 0,
              }}
            >
              <CloseIconSvg />
            </IconButton>
          </Box>
        );
      }
    }
    return null;
  }, [selectedFile]);

  const chatStatus =
    formData?.contacts?.length > 0
      ? formData?.contacts?.every(
          (contact: any) => contact?.chatStatus === "open"
        )
      : [];

  // all useEffects

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  async function GetFileByBlobId(id: any) {
    const data = await dispatch(getFileByBlobId(id));
    return data;
  }
  const debouncedLoadContactsRef = useRef<any>(null);
  const searchQueryRef = useRef(searchContactQuery);
  const filterDataRef = useRef(filterData);
  const chatStatusFilterRef = useRef(chatStatusFilter);

  // Update refs when values change
  useEffect(() => {
    searchQueryRef.current = searchContactQuery;
  }, [searchContactQuery]);

  useEffect(() => {
    filterDataRef.current = filterData;
  }, [filterData]);

  useEffect(() => {
    chatStatusFilterRef.current = chatStatusFilter;
  }, [chatStatusFilter]);

  const loadContacts = useCallback(async () => {
    setloadingContacts(true);
    try {
      const bodyData = {
        businessId: userInfo?.companyId,
        userId: userInfo?.userId,
        page: contactsPage,
        per_page: contactsPerPage,

        data: {
          searching: {
            value: searchQueryRef.current,
          },
          filtering: {
            filterType: "and",
            conditions: [
              {
                column: "ChatStatus",
                operator: "equals",
                value: [
                  chatStatusFilterRef.current === "All"
                    ? ""
                    : chatStatusFilterRef.current,
                ],
              },
              {
                column: "Tags",
                operator: "contains",
                value: [filterDataRef.current],
              },
            ],
          },
        },
      };
      const response = await dispatch(fetchAllContacts(bodyData));
      setContacts(response?.payload?.data);
      const pageCount =
        response?.payload?.total % 9 === 0
          ? Math.floor(response?.payload?.total / 9)
          : Math.floor(response?.payload?.total / 9) + 1;
      setContactsPageCount(pageCount);
    } catch (error) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: `Error loading contacts`,
        })
      );
    } finally {
      setloadingContacts(false);
    }
  }, [
    userInfo?.companyId,
    userInfo?.userId,
    contactsPage,
    contactsPerPage,
    dispatch,
  ]);

  // Initialize the debounced function only once
  useEffect(() => {
    debouncedLoadContactsRef.current = debounce(loadContacts, 1000);

    // Cleanup function to cancel any pending debounced calls
    return () => {
      if (debouncedLoadContactsRef.current) {
        debouncedLoadContactsRef.current.cancel();
      }
    };
  }, []); // Empty dependency array - only run once

  const processData = async () => {
    try {
      if (data && title !== "Add") {
        setSwitchChecked(true);
        if (data?.audiance?.split(",").length > 0) {
          setChoosedAudience((prev: any) => {
            return {
              ...prev,
              showAutoComplete: true,
            };
          });
        }
        if (data?.uploadedFileId) {
          setChoosedAudience((prev: any) => {
            return {
              ...prev,
              showFileUpload: true,
            };
          });
        }

        let searchContactId: any = [];

        if (data?.audiance?.split(",").length > 0) {
          const contactsResponse = await CAMPAIGN_API.getContactsById({
            contactIds: data?.audiance?.split(","),
          });
          searchContactId = contactsResponse?.data?.data;
        }

        let contactsBlobFileName = null;
        if (data?.uploadedFileId) {
          contactsBlobFileName = await GetFileByBlobId(data?.uploadedFileId);
        }

        const utcDateTime = data?.dateSetLive;
        const localDateTime = moment.utc(utcDateTime).local();
        const localDate = localDateTime.format("YYYY-MM-DD");
        const istDateTime = moment.utc(utcDateTime).tz("Asia/Kolkata");

        setFormData({
          ...formData,
          name: data?.campaignTitle,
          contacts: searchContactId,
          importedContactsFile: contactsBlobFileName?.payload?.data?.fileName,
          importedContactsFileId: data?.uploadedFileId,
          importedContactsFileUrl:
            contactsBlobFileName?.payload?.data?.filePath,
          message: data?.sendTextType,
          selectedDate: localDate,
          setSelectedTime: dayjs(istDateTime.format()),
        });
        const automation = JSON.parse(data?.automationJson);

        setCampaignState((prev) => ({
          ...prev,
          workflowAutomation: {
            input: automation?.WorkflowAutomation?.Input || "",
            responseType: automation?.WorkflowAutomation?.ResponseType || "",
            customerResponse:
              automation?.WorkflowAutomation?.CustomerResponse || "",
            workflowName: automation?.WorkflowAutomation?.WorkflowName || "",
            contactIds: automation?.WorkflowAutomation?.ContactsIds || [],
          },
          name: automation?.Name,
          autoCustomAutomation: {
            input: automation?.AutoCustomAutomation?.Input || "",
            responseType: automation?.AutoCustomAutomation?.ResponseType || "",
            customerResponse:
              automation?.AutoCustomAutomation?.CustomerResponse || "",
            bodyMessage: automation?.AutoCustomAutomation?.BodyMessage || "",
          },
        }));

        if (data?.templateId) {
          const params = {
            userId: data?.userId,
            businessId: data?.businessId,
            templateId: data?.templateId,
          };
          templateDataById(params); // Assuming `templateDataById` is an async function.
        }

        setSelectedFile(data?.mediaUrl || null);
      }
    } catch (err) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: "Error processing data",
        })
      );
    }
  };

  useEffect(() => {
    if (open && debouncedLoadContactsRef.current) {
      debouncedLoadContactsRef.current();
    }
  }, [filterData, chatStatusFilter, contactsPage, searchContactQuery, open]);

  useEffect(() => {
    processData();
  }, [data, title]);

  const uploadFile = async () => {
    if (selectedFile && typeof selectedFile !== "string" && !sendFileToApi) {
      setIsUploading(true);
      try {
        setIsEditLoading(true);
        const data = { mediaFile: selectedFile };
        const response = await CAMPAIGN_API.uploadCampaignMedia(data);
        if (response && response?.data) {
          setSendFileToApi(response?.data);
        } else {
        }
      } catch (error) {
      } finally {
        setIsUploading(false);
        setIsEditLoading(false);
      }
    }
  };

  useEffect(() => {
    uploadFile();
  }, [selectedFile, sendFileToApi]);
  const fetchData = async () => {
    const data = {
      businessId: userInfo?.companyId,
      campaignTitle: titleSearch,
    };

    await debouncedFetchCampaigns(data);
  };

  useEffect(() => {
    if (title === "Edit") {
      if (formData?.name?.trim() !== data?.campaignTitle) {
        setExistingCampaignName(true);
        fetchData();
        // setValidCampaignMessage(searchResult?.message);
      } else {
        setFormErrors({ ...formErrors, name: "" });

        setExistingCampaignName(false);
      }
    } else {
      if (titleSearch && title !== "Edit") {
        fetchData();
      }
    }
  }, [titleSearch]);

  useEffect(() => {
    if (formTouched) {
      isValidForm();
    }
  }, [formData, searchStatus]);

  return (
    <Dialog
      open={open}
      onClose={handleCloseAll}
      PaperProps={{ style: { minWidth: "80%", borderRadius: "20px" } }}
    >
      <DialogTitle>
        <Box m={2} mb={0} className={classes.changePasswordContainer}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography
              variant="h5"
              style={{
                color: "#000000",
                fontWeight: "600",
                fontSize: "20px",
                display: "flex",
              }}
            >
              <span style={{ marginTop: "2px" }}>
                <CampaignIcon sx={{ height: "22px" }} />
              </span>{" "}
              &nbsp; {title} Campaign
            </Typography>
            <Tooltip
              title={
                <>
                  <div>
                    1. We can only send message and media campaigns to contacts
                    with chat status open only.
                  </div>
                  <div>
                    2. We can only send campaigns to contacts with OptIn status
                    active only.
                  </div>
                </>
              }
              arrow
            >
              <IconButton size="small">
                <InfoIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
          <Box onClick={handleCloseAll} sx={{ cursor: "pointer" }}>
            <CloseSvg />
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent>
        {getCampaignByIdStatus === "loading" ? (
          <>
            {title === "Add" ? (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  marginBottom: "40px",
                  height: "300px",
                }}
              >
                <LoadingComponent height="auto" color={bgColors?.blue} />
                <Typography sx={{ fontSize: 12 }}>
                  Loading contacts...
                </Typography>
              </Box>
            ) : (
              <LoadingComponent height="300px" color={bgColors?.blue} />
            )}
          </>
        ) : (
          <>
            <Box mb={2} mt={2}>
              {/* <InputLabel className={classes.blackColor}>
                Campaign Title
              </InputLabel> */}
              <TextFeildWithBorderComponet
                // style={{ }}
                label="Enter your campaign title"
                name="name"
                placeholder=""
                value={formData?.name}
                // error={!!formErrors.name}
                disabled={title === "Edit"}
                error={
                  title === "Edit"
                    ? !!formErrors.name &&
                      formData?.name !== data?.campaignTitle
                    : title === "Add" && !!formErrors.name
                }
                // helperText={formErrors?.name}
                helperText={
                  searchStatus === "idle" ||
                  searchStatus === "loading" ||
                  formData?.name === data?.campaignTitle
                    ? ""
                    : formErrors?.name
                  //  || // (formData?.name &&
                  //   formErrors.name === "" &&
                  //   formTouched === true &&
                  //   existingCampaignName === true &&
                  //   searchResult &&
                  //   `${titleSearch} campaign already exists.`)
                  // validCampaignMessage
                }
                onChange={handleTextChange}
                sx={{
                  "& input": {
                    // fontWeight: "600",
                  },
                  "& .MuiFormHelperText-root": {
                    color: formErrors.name ? "red" : "green", // Color based on success or error
                  },
                }}
                InputProps={{
                  sx: { fontSize: 14 },
                  endAdornment: (
                    <>
                      {searchStatus === "loading" && (
                        <CircularProgress size={20} />
                      )}
                    </>
                  ),
                }}
              />
            </Box>
            <ContactSelectionOption
              classes={classes}
              formData={formData}
              setFormData={setFormData}
              formErrors={formErrors}
              setFormErrors={setFormErrors}
              setAnchorElement={setAnchorElement}
              choosedAudience={choosedAudience}
              setChoosedAudience={setChoosedAudience}
              contacts={contacts}
              setContacts={setContacts}
              loadingContacts={loadingContacts}
              setloadingContacts={setloadingContacts}
              searchContactQuery={searchContactQuery}
              setSearchContactQuery={setSearchContactQuery}
              contactsPage={contactsPage}
              setContactsPage={setContactsPage}
              contactsPageCount={contactsPageCount}
              setContactsPageCount={setContactsPageCount}
              filterData={filterData}
              setFilterData={setFilterData}
              chatStatusFilter={chatStatusFilter}
              setChatStatusFilter={setChatStatusFilter}
              setFilter={setFilter}
              setStatusFilter={setStatusFilter}
            />
            <Box onClick={(e) => e.stopPropagation()}>
              {(formData?.contacts?.length === 0 || chatStatus) &&
              !choosedAudience?.showFileUpload ? (
                <CommonAccordion
                  title="Message"
                  expanded={expanded === "panel1"}
                  onChange={handleAccordionChange("panel1")}
                  isActive={expanded === "panel1"}
                >
                  <Box mb={2} mt={2}>
                    {/* file Input */}

                    {selectedFile && (
                      <Box
                        sx={{
                          position: "relative",
                          alignItems: "center",
                          bottom: "0px",
                        }}
                      >
                        {(selectedFile?.type?.startsWith("image/") ||
                          (typeof selectedFile === "string" &&
                            selectedFile?.endsWith(".png")) ||
                          (typeof selectedFile === "string" &&
                            selectedFile?.endsWith(".jpg")) ||
                          (typeof selectedFile === "string" &&
                            selectedFile?.endsWith(".jpeg")) ||
                          (mediaType && mediaType?.startsWith("image/"))) && (
                          <Box
                            sx={{
                              position: "relative",
                              display: "inline-block",
                            }}
                          >
                            <img
                              src={
                                typeof selectedFile === "string"
                                  ? selectedFile
                                  : URL.createObjectURL(selectedFile)
                              }
                              alt="Selected File"
                              // style={{
                              //   maxHeight: 80,
                              //   maxWidth: "auto",
                              // }}
                              style={{ height: "100px", width: "200px" }}
                              // controls
                            />
                            <IconButton
                              onClick={handleCancel}
                              sx={{
                                position: "absolute",
                                top: 0,
                                right: 0,
                              }}
                            >
                              <CloseIconSvg />
                            </IconButton>
                          </Box>
                        )}
                        {/* {(selectedFile?.type?.startsWith("video/mp4") ||
                        (typeof selectedFile === "string" &&
                          selectedFile?.endsWith(".mp4")) ||
                        (typeof selectedFile === "string" &&
                          selectedFile?.endsWith(".mov")) ||
                        (typeof selectedFile === "string" &&
                          selectedFile?.endsWith(".mkv")) ||
                        (mediaType && mediaType?.startsWith("video/mp4"))) && (
                        <Box
                          sx={{ position: "relative", display: "inline-block" }}
                        >
                          <video
                            controls
                            src={
                              typeof selectedFile === "string"
                                ? selectedFile
                                : URL.createObjectURL(selectedFile)
                            }
                            style={{ height: "100px", width: "200px" }}
                          />
                          <IconButton
                            onClick={handleCancel}
                            sx={{
                              position: "absolute",
                              top: 0,
                              right: 0,
                            }}
                          >
                            <CloseIconSvg />
                          </IconButton>
                        </Box>
                      )} */}
                        {
                          // selectedFile.type === "application/pdf" ||
                          // selectedFile.type ===
                          //   "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
                          // selectedFile.type ===
                          //   "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
                          // selectedFile.type === "text/plain" ||
                          // ((mediaType && mediaType === "text/plain") ||
                          //   mediaType === "application/pdf" ||
                          //   mediaType ===
                          //     "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
                          //   mediaType ===
                          //     "application/vnd.openxmlformats-officedocument.wordprocessingml.document") && (
                          (selectedFile?.type?.startsWith("application/pdf") ||
                            (typeof selectedFile === "string" &&
                              selectedFile?.endsWith(".pdf")) ||
                            (mediaType &&
                              mediaType?.startsWith("application/pdf"))) && (
                            <Box
                              p={1}
                              mr={4}
                              sx={{
                                width: "200px",
                                overflow: "hidden",
                                whiteSpace: "nowrap",
                                textOverflow: "ellipsis",
                                position: "relative",
                                display: "inline-block",
                              }}
                            >
                              {/* <a
                            href={
                              typeof selectedFile === "string"
                                ? selectedFile
                                : URL.createObjectURL(selectedFile)
                            }
                            download={selectedFile.name}
                            style={{ fontSize: "14px" }}
                          >
                            {selectedFile.name}
                          </a> */}
                              {/* <iframe
                              src={
                                typeof selectedFile === "string"
                                  ? selectedFile
                                  : URL.createObjectURL(selectedFile)
                              }
                              
                              style={{ height: "100px", width: "200px" }}
                            ></iframe> */}
                              <IconButton
                                onClick={handleCancel}
                                sx={{
                                  position: "absolute",
                                  top: 0,
                                  right: 0,
                                }}
                              >
                                <CloseIconSvg />
                              </IconButton>
                            </Box>
                          )
                        }
                      </Box>
                    )}

                    {/* text Input */}

                    <div style={{ position: "relative", marginTop: "10px" }}>
                      {!templateData?.templateId ? (
                        <Grid item xs={12}>
                          {memoizedMediaPlayer}
                          <TextField
                            fullWidth
                            ref={messageRef}
                            variant="outlined"
                            size="small"
                            name="message"
                            multiline
                            rows={6}
                            value={formData?.message}
                            error={!!formErrors.message}
                            helperText={formErrors?.message}
                            onChange={handleTextChange}
                            // onKeyDown={handleKeyDown}
                            placeholder="Write a response..."
                            // placeholder={message.trim().length > 0 ? '' : ' '}
                            // className={`${classes.inputtest} custom-placeholder`}
                            sx={{
                              "& .MuiInputBase-root": {
                                backgroundColor: "#f3f3f3",
                                // minHeight: selectedFile ? 200 : 40,
                                padding: "2px",

                                alignItems: "flex-end",
                              },
                              "& .MuiOutlinedInput-root": {
                                "&:hover fieldset": {
                                  // borderColor: "#4B5A5A", // Change the hover border color here
                                },
                                "&.Mui-focused fieldset": {
                                  borderColor: "#4B5A5A", // Change the focused border color here
                                },
                              },
                              "& .MuiInputBase-input": {
                                "&::placeholder": {
                                  color: "black", // Change the placeholder color here
                                  fontFamily: "inherit",
                                  // fontWeight:"100",
                                  fontSize: "14px",
                                },
                              },
                              "& ::placeholder": {
                                // color: "#4B5A5A !important", // Change the placeholder color here
                                color: "black !important", // Change the placeholder color here
                              },
                            }}
                            InputProps={{
                              style: {
                                backgroundColor: "#f3f3f3",
                                // minHeight: selectedFile ? 400 : 40,
                                padding: "2px",
                              }, // Increase text field height
                              startAdornment: (
                                <InputAdornment position="start">
                                  <Box
                                    style={{
                                      display: "flex",
                                      flexDirection: "column",
                                    }}
                                  >
                                    <Box
                                      style={{
                                        display: "flex",
                                        flexDirection: "row",
                                        position: "absolute",
                                        right: "10px",
                                        bottom: "5px",
                                      }}
                                      // ml={0.5}
                                      // mt={0.8}
                                    >
                                      <>
                                        <Box
                                          pr={1}
                                          className={classes.cursor}
                                          // onClick={(e) => {
                                          //   openDialog ? "" : handleEmojiClick(e);
                                          // }}
                                          onClick={(e) => {
                                            if (!openDialog) {
                                              handleEmojiClick(e);
                                            }
                                          }}
                                        >
                                          <ChatEmojiIcon
                                            //  fillColor="rgba(0, 0, 0, 0.35)"
                                            fillColor={
                                              openDialog ? "#cdcdcd" : "#4B5A5A"
                                            }
                                          />
                                        </Box>
                                        <EmojiPopover
                                          open={emojiPopoverOpen}
                                          anchorEl={anchorEl}
                                          onClose={handleCloseEmojiPopover}
                                          onEmojiSelect={handleEmojiSelect}
                                        />
                                        <Box
                                          pr={1}
                                          className={classes.cursor}
                                          // onClick={() => {
                                          //   openDialog
                                          //     ? ""
                                          //     : handleFileIconClick();
                                          // }}
                                          onClick={() => {
                                            if (!openDialog) {
                                              handleFileIconClick();
                                            }
                                          }}
                                        >
                                          <ChatFileIcon
                                            //  fill={iconColor}
                                            fill={
                                              openDialog ? "#cdcdcd" : "#4B5A5A"
                                            }
                                          />
                                          <input
                                            ref={fileInputRef}
                                            type="file"
                                            multiple
                                            accept=".pdf,.doc,.ppt,image/*,video/*"
                                            style={{ display: "none" }}
                                            onChange={handleFileChange}
                                          />
                                        </Box>
                                      </>

                                      <Box
                                        pr={1}
                                        className={
                                          formData?.message?.length === 0 &&
                                          (selectedFile === null ||
                                            selectedFile === undefined)
                                            ? classes.cursor
                                            : ""
                                        }
                                        // onClick={() => {
                                        //   formData?.message?.length === 0 &&
                                        //   (selectedFile === null ||
                                        //     selectedFile === undefined)
                                        //     ? handleOpenDialog1()
                                        //     : null;
                                        // }}
                                        onClick={() => {
                                          if (
                                            formData?.message?.length === 0 &&
                                            (selectedFile === null ||
                                              selectedFile === undefined)
                                          ) {
                                            handleOpenDialog1();
                                          }
                                        }}
                                      >
                                        <ChatTemplate
                                          // fillColor="rgba(0, 0, 0, 0.35)"
                                          fillColor={
                                            formData?.message?.length === 0 &&
                                            (selectedFile === null ||
                                              selectedFile === undefined)
                                              ? "#4B5A5A"
                                              : "#cdcdcd"
                                          }
                                        />
                                      </Box>
                                    </Box>
                                  </Box>
                                </InputAdornment>
                              ),
                            }}
                          />
                        </Grid>
                      ) : (
                        <Grid item xs={12}>
                          <Box
                            sx={{
                              border: "1px solid #d3d3d3",
                              borderRadius: "10px",
                            }}
                          >
                            <IconButton
                              aria-label="close"
                              sx={{
                                position: "absolute",
                                top: "5px",
                                right: "5px",
                                zIndex: 1,
                              }}
                              onClick={() => {
                                setFormData((prev: any) => ({
                                  ...prev,
                                  message: "",
                                }));
                                setTemplateData({});
                                setCampaignState({
                                  name: "",
                                  autoCustomAutomation: {
                                    input: "",
                                    responseType: ResponseType.CustomMessage,
                                    customerResponse:
                                      CustomerResponse.OnButtonClick,
                                    bodyMessage: "",
                                  },
                                });
                              }}
                            >
                              <CloseIcon sx={{ fontSize: "20px" }} />
                            </IconButton>
                            <TemplatePreviewLayout
                              header={templateData?.header || ""}
                              body={templateData?.body || ""}
                              footer={templateData?.footer || ""}
                              mediaType={templateData?.mediaType || null}
                              // mediaFile={templateData?.mediaFile || null}
                              mediaFile={
                                templateData?.mediaAwsUrl ||
                                templateData?.mediaFile ||
                                null
                              }
                              buttons={templateData?.buttons || []}
                            />
                          </Box>
                        </Grid>
                      )}
                    </div>
                    {/* < TemplatePreviewLayout/> */}
                  </Box>
                </CommonAccordion>
              ) : (
                <Box sx={{ mb: 3 }}>
                  {!templateData?.templateId ? (
                    <Button
                      sx={{
                        justifyContent: "center",
                        backgroundColor: bgColors?.gray2,
                        border: `0.5px solid ${bgColors?.gray5}`,
                        "&:hover": {
                          backgroundColor: bgColors?.gray5,
                          // color: "#ffffff",
                        },
                        color: bgColors?.green,
                        fontSize: 10,
                        mt: 2,
                        width: "100%",
                      }}
                      onClick={handleOpenDialog1}
                    >
                      Send Template
                    </Button>
                  ) : (
                    <>
                      <InputLabel className={classes.blackColor}>
                        Text Message
                      </InputLabel>
                      <Box
                        sx={{
                          border: "1px solid #d3d3d3",
                          borderRadius: "10px",
                        }}
                      >
                        <IconButton
                          aria-label="close"
                          // sx={{
                          //   position: "absolute",
                          //   top: "5px",
                          //   right: "5px",
                          //   zIndex: 1,
                          // }}
                          onClick={() => {
                            setTemplateData({});
                          }}
                        >
                          <CloseIcon sx={{ fontSize: "20px" }} />
                        </IconButton>
                        <TemplatePreviewLayout
                          header={templateData?.header || ""}
                          body={templateData?.body || ""}
                          footer={templateData?.footer || ""}
                          mediaType={templateData?.mediaType || null}
                          // mediaFile={templateData?.mediaFile || null}
                          mediaFile={
                            templateData?.mediaAwsUrl ||
                            templateData?.mediaFile ||
                            null
                          }
                          buttons={templateData?.buttons || []}
                        />
                      </Box>
                    </>
                  )}
                </Box>
              )}
              <TemplatePopUp
                open={openDialog}
                handleCloseTemplatePopup={handleCloseDialog}
                setSendTemplatePayload={setSendTemplatePayload}
                formData={formData}
              />

              <CommonAccordion
                title="Set next steps (Recommended)"
                expanded={expanded === "panel2"}
                onChange={handleAccordionChange("panel2")}
                isActive={expanded === "panel2"}
                listItems={[
                  {
                    title: "Send Custom Reply",
                    subTitle: "Auto-send the list of FAQs set up here",
                    actionButton: campaignState?.autoCustomAutomation?.input ? (
                      <Box display="flex" flexDirection="column">
                        <Typography
                          variant="h6"
                          style={{ color: `${bgColors.green}` }}
                        >
                          Flow added
                        </Typography>
                        <Box display="flex" justifyContent="end" gap={1}>
                          <Typography
                            onClick={() => handleOpenCustomReplyDialog()}
                            style={{
                              color: `${bgColors.blue2}`,
                              cursor: "pointer",
                              textDecoration: "underline",
                            }}
                          >
                            Edit
                          </Typography>
                          <Typography
                            onClick={handleDeleteCustomReply}
                            style={{
                              color: `${bgColors.red1}`,
                              cursor: "pointer",
                              textDecoration: "underline",
                            }}
                          >
                            Delete
                          </Typography>
                        </Box>
                      </Box>
                    ) : (
                      <button
                        onClick={() => handleOpenCustomReplyDialog()}
                        className={classes.SaveChangesButton}
                      >
                        + Set up
                      </button>
                    ),
                  },
                ]}
              />
              <CustomReplyPopUpCampiagn
                open={isCustomReplyDialogOpen}
                onClose={handleCloseCampaignReplyPopup}
                campaignState={campaignState}
                templateData={templateData}
                setTemplateData={setTemplateData}
                setCampaignState={setCampaignState}
                ResponseType={ResponseType}
              />
            </Box>

            <Box mt={3} className={classes.account}>
              <Box
                display="flex"
                flexDirection="row"
                // justifyContent="space-between"
              >
                <Box>
                  <Typography variant="body1" className={classes.switch}>
                    Schedule for Later
                  </Typography>
                </Box>
                <Box ml={2} style={{ marginTop: "-7px" }}>
                  <FormControlLabel
                    label=""
                    control={
                      <Switch
                        checked={isSwitchChecked}
                        onChange={handleSwitchChange}
                        color="success"
                        sx={{
                          "&.Mui-unchecked .MuiSwitch-thumb": {
                            color: "white",
                          },
                          "& .MuiSwitch-track": {
                            backgroundColor: "#3CAA93",
                          },
                          "&.Mui-checked .MuiSwitch-thumb": {
                            color: "green",
                          },
                          "&.Mui-checked + .MuiSwitch-track": {
                            backgroundColor: "green",
                          },
                        }}
                      />
                    }
                  />
                  <Box
                    ml={"-160px"}
                    mt={"20px"}
                    style={{
                      display: isSwitchChecked ? "flex" : "none",
                      flexDirection: "row",
                      gap: 20,
                    }}
                  >
                    <TextField
                      id="date"
                      label="Date"
                      type="date"
                      name="date"
                      // defaultValue="2024-04-24"
                      value={
                        isSwitchChecked ? formData?.selectedDate || "" : ""
                      }
                      onChange={handleDateChange}
                      inputProps={{
                        min: getCurrentDate(),
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      // className={classes.dateField}
                      sx={{
                        width: 145,
                        top: 7,
                        height: "35px", // set desired height here
                        "& .MuiInputBase-root": {
                          height: "100%",
                        },
                      }}
                    />
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DemoContainer components={["TimePicker"]}>
                        <FormControl
                          sx={{
                            height: "25%",
                            width: "140px",
                            bottom: "0px",
                          }}
                        >
                          <TimePicker
                            label="Time"
                            className={classes.timefield}
                            // defaultValue={dayjs(formData.setSelectedTime)}
                            value={
                              isSwitchChecked ? formData.setSelectedTime : null
                            }
                            onChange={handleTimeChange}
                            minutesStep={1}
                            minTime={
                              isSwitchChecked &&
                              formData.selectedDate === getCurrentDate()
                                ? dayjs()
                                : undefined
                            }
                            // ampm={false}
                            sx={{
                              height: "35px", // set desired height here
                              "& .MuiInputBase-root": {
                                height: "100%",
                                overflow: "hidden",
                              },
                            }}
                          />
                        </FormControl>
                      </DemoContainer>
                    </LocalizationProvider>
                  </Box>
                </Box>
                {/* {isSwitchChecked && (
                
                <Box style={{ display: "flex", flexDirection: "row" }}>
                  <Box>
                    <TextField
                      id="date"
                      label="Date"
                      type="date"
                      defaultValue="2023-02-02"
                      InputLabelProps={{
                        shrink: true,
                      }}
                      className={classes.dateField}
                    />
                  </Box>
                  <Box ml={2} mb={-5} mt={-1}>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DemoContainer components={['TimePicker']}>
                        <FormControl
                          sx={{
                            height: "30%",
                            width: "150px"
                          }}
                        >
                          <TimePicker
                            label="Time"                       
                          />
                        </FormControl>
                      </DemoContainer>
                    </LocalizationProvider>
                  </Box>
                </Box>
              )} */}
              </Box>
            </Box>

            <Box>
              <Popover
                open={Boolean(anchorElement)}
                anchorEl={anchorElement}
                onClose={handleCloseMainPopover}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "right",
                }}
                transformOrigin={{
                  vertical: "top",
                  horizontal: "right",
                }}
              >
                <Box className={classes.popoverContainer}>
                  <Box className={classes.headerContainer}>
                    <Typography
                      sx={{ fontSize: "14px" }}
                      className={classes.title}
                    >
                      Select filters
                    </Typography>
                    <IconButton
                      onClick={handleCloseMainPopover}
                      className={classes.closeButton}
                    >
                      {/* <ChatFilterCloseIconSvg /> */}
                      <CloseSvg />
                    </IconButton>
                  </Box>
                  <Box className={classes.selectContainer}>
                    <Typography
                      sx={{ fontSize: "14px" }}
                      mb={1}
                      className={classes.textColor}
                    >
                      Chat Status
                    </Typography>
                    <Select
                      value={statusFilter}
                      // onChange={handleChangeStatusFilter("chatStatus")}
                      onChange={handleChangeStatusFilter()}
                      fullWidth
                      variant="outlined"
                      size="small"
                      sx={{ fontSize: "12px" }}
                    >
                      {/* <MenuItem value="All" sx={{ fontSize: "12px" }}>
              All
            </MenuItem> */}
                      {/* Map through tagsArray and create MenuItem for each tag */}
                      {["open", "close"]?.length !== 0 ? (
                        ["All", "Open", "New", "Expired", "Resolved"]?.map(
                          (tag: any) => (
                            <MenuItem
                              key={tag?.id}
                              value={tag}
                              sx={{ fontSize: "12px" }}
                            >
                              {tag}
                            </MenuItem>
                          )
                        )
                      ) : (
                        <Typography sx={{ fontSize: "12px" }}>
                          No Data
                        </Typography>
                      )}
                    </Select>
                  </Box>
                  <Box className={classes.selectContainer}>
                    <Typography
                      sx={{ fontSize: "14px" }}
                      mb={1}
                      className={classes.textColor}
                    >
                      Tags
                    </Typography>
                    <Select
                      value={filters}
                      // onChange={handleChangeFilters("tags")}
                      onChange={handleChangeFilters()}
                      fullWidth
                      variant="outlined"
                      size="small"
                      sx={{ fontSize: "12px" }}
                    >
                      {/* <MenuItem value="All" sx={{ fontSize: "12px" }}>
              All
            </MenuItem> */}
                      {/* Map through tagsArray and create MenuItem for each tag */}
                      {tagsArray?.length !== 0 ? (
                        tagsArray?.map((tag: any) => (
                          <MenuItem
                            key={tag?.id}
                            value={tag?.id}
                            sx={{ fontSize: "12px" }}
                          >
                            {tag?.tag}
                          </MenuItem>
                        ))
                      ) : (
                        <Typography sx={{ fontSize: "12px" }}>
                          No Data
                        </Typography>
                      )}
                    </Select>
                  </Box>
                  <Box className={classes.buttonContainer}>
                    <Box m={2}>
                      <button
                        className={classes.resetBtnStyles}
                        onClick={handleResetFilter}
                      >
                        Reset
                      </button>
                    </Box>
                    <Box m={2}>
                      <button
                        className={classes.SaveChangesButton}
                        onClick={handleApplyFilter}
                      >
                        Apply Filters
                      </button>
                    </Box>
                  </Box>
                </Box>
              </Popover>
            </Box>
          </>
        )}
      </DialogContent>
      <DialogActions>
        <Grid item xs={12} mb={4} ml={4} mr={4}>
          {createCamapignStatus === "loading" || isEditLoading ? (
            <LoadingComponent height="auto" color={bgColors?.blue} />
          ) : (
            <LoadingButton
              //  loading={createCamapignStatus === "loading"}
              onClick={handleSave}
              // className={classes.button}
              sx={{
                backgroundColor: `${bgColors.black} !important`,
                height: "40px",
                color: "white !important",
                width: "100% !important",
                fontSize: "14px !important",
                fontWeight: "Semi Bold !important",
                borderRadius: "8px !important",
              }}
            >
              Start Campaign
            </LoadingButton>
          )}
        </Grid>
      </DialogActions>
    </Dialog>
  );
};

export default EditCampaign;
